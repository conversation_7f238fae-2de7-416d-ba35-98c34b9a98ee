package util

import (
	"sync"
)

var DefaultMimeMap map[string]string
var once sync.Once

func LoadMimeMap() {
	DefaultMimeMap = make(map[string]string)
	DefaultMimeMap[".ez"] = "application/andrew-inset"
	DefaultMimeMap[".atom"] = "application/atom+xml"
	DefaultMimeMap[".atomcat"] = "application/atomcat+xml"
	DefaultMimeMap[".atomsvc"] = "application/atomsvc+xml"
	DefaultMimeMap[".ccxml"] = "application/ccxml+xml"
	DefaultMimeMap[".davmount"] = "application/davmount+xml"
	DefaultMimeMap[".ecma"] = "application/ecmascript"
	DefaultMimeMap[".pfr"] = "application/font-tdpfr"
	DefaultMimeMap[".stk"] = "application/hyperstudio"
	DefaultMimeMap[".js"] = "text/javascript"
	DefaultMimeMap[".json"] = "text/json"
	DefaultMimeMap[".hqx"] = "application/mac-binhex40"
	DefaultMimeMap[".cpt"] = "application/mac-compactpro"
	DefaultMimeMap[".mrc"] = "application/marc"
	DefaultMimeMap[".ma"] = "application/mathematica"
	DefaultMimeMap[".nb"] = "application/mathematica"
	DefaultMimeMap[".mb"] = "application/mathematica"
	DefaultMimeMap[".mathml"] = "application/mathml+xml"
	DefaultMimeMap[".mbox"] = "application/mbox"
	DefaultMimeMap[".mscml"] = "application/mediaservercontrol+xml"
	DefaultMimeMap[".mp4s"] = "application/mp4"
	DefaultMimeMap[".doc"] = "application/msword"
	DefaultMimeMap[".dot"] = "application/msword"
	DefaultMimeMap[".mxf"] = "application/mxf"
	DefaultMimeMap[".bin"] = "application/octet-stream"
	DefaultMimeMap[".dms"] = "application/octet-stream"
	DefaultMimeMap[".lha"] = "application/octet-stream"
	DefaultMimeMap[".lzh"] = "application/octet-stream"
	DefaultMimeMap[".class"] = "application/octet-stream"
	DefaultMimeMap[".so"] = "application/octet-stream"
	DefaultMimeMap[".iso"] = "application/octet-stream"
	DefaultMimeMap[".dmg"] = "application/octet-stream"
	DefaultMimeMap[".dist"] = "application/octet-stream"
	DefaultMimeMap[".distz"] = "application/octet-stream"
	DefaultMimeMap[".pkg"] = "application/octet-stream"
	DefaultMimeMap[".bpk"] = "application/octet-stream"
	DefaultMimeMap[".dump"] = "application/octet-stream"
	DefaultMimeMap[".elc"] = "application/octet-stream"
	DefaultMimeMap[".oda"] = "application/oda"
	DefaultMimeMap[".ogg"] = "application/ogg"
	DefaultMimeMap[".pdf"] = "application/pdf"
	DefaultMimeMap[".pgp"] = "application/pgp-encrypted"
	DefaultMimeMap[".asc"] = "application/pgp-signature"
	DefaultMimeMap[".sig"] = "application/pgp-signature"
	DefaultMimeMap[".prf"] = "application/pics-rules"
	DefaultMimeMap[".p10"] = "application/pkcs10"
	DefaultMimeMap[".p7m"] = "application/pkcs7-mime"
	DefaultMimeMap[".p7c"] = "application/pkcs7-mime"
	DefaultMimeMap[".p7s"] = "application/pkcs7-signature"
	DefaultMimeMap[".cer"] = "application/pkix-cert"
	DefaultMimeMap[".crl"] = "application/pkix-crl"
	DefaultMimeMap[".pkipath"] = "application/pkix-pkipath"
	DefaultMimeMap[".pki"] = "application/pkixcmp"
	DefaultMimeMap[".pls"] = "application/pls+xml"
	DefaultMimeMap[".ai"] = "application/postscript"
	DefaultMimeMap[".eps"] = "application/postscript"
	DefaultMimeMap[".ps"] = "application/postscript"
	DefaultMimeMap[".cww"] = "application/prs.cww"
	DefaultMimeMap[".rdf"] = "application/rdf+xml"
	DefaultMimeMap[".rif"] = "application/reginfo+xml"
	DefaultMimeMap[".rnc"] = "application/relax-ng-compact-syntax"
	DefaultMimeMap[".rl"] = "application/resource-lists+xml"
	DefaultMimeMap[".rs"] = "application/rls-services+xml"
	DefaultMimeMap[".rsd"] = "application/rsd+xml"
	DefaultMimeMap[".rss"] = "application/rss+xml"
	DefaultMimeMap[".rtf"] = "application/rtf"
	DefaultMimeMap[".sbml"] = "application/sbml+xml"
	DefaultMimeMap[".scq"] = "application/scvp-cv-request"
	DefaultMimeMap[".scs"] = "application/scvp-cv-response"
	DefaultMimeMap[".spq"] = "application/scvp-vp-request"
	DefaultMimeMap[".spp"] = "application/scvp-vp-response"
	DefaultMimeMap[".sdp"] = "application/sdp"
	DefaultMimeMap[".setpay"] = "application/set-payment-initiation"
	DefaultMimeMap[".setreg"] = "application/set-registration-initiation"
	DefaultMimeMap[".shf"] = "application/shf+xml"
	DefaultMimeMap[".smi"] = "application/smil+xml"
	DefaultMimeMap[".smil"] = "application/smil+xml"
	DefaultMimeMap[".rq"] = "application/sparql-query"
	DefaultMimeMap[".srx"] = "application/sparql-results+xml"
	DefaultMimeMap[".gram"] = "application/srgs"
	DefaultMimeMap[".grxml"] = "application/srgs+xml"
	DefaultMimeMap[".ssml"] = "application/ssml+xml"
	DefaultMimeMap[".plb"] = "application/vnd.3gpp.pic-bw-large"
	DefaultMimeMap[".psb"] = "application/vnd.3gpp.pic-bw-small"
	DefaultMimeMap[".pvb"] = "application/vnd.3gpp.pic-bw-var"
	DefaultMimeMap[".tcap"] = "application/vnd.3gpp2.tcap"
	DefaultMimeMap[".pwn"] = "application/vnd.3m.post-it-notes"
	DefaultMimeMap[".aso"] = "application/vnd.accpac.simply.aso"
	DefaultMimeMap[".imp"] = "application/vnd.accpac.simply.imp"
	DefaultMimeMap[".acu"] = "application/vnd.acucobol"
	DefaultMimeMap[".atc"] = "application/vnd.acucorp"
	DefaultMimeMap[".acutc"] = "application/vnd.acucorp"
	DefaultMimeMap[".xdp"] = "application/vnd.adobe.xdp+xml"
	DefaultMimeMap[".xfdf"] = "application/vnd.adobe.xfdf"
	DefaultMimeMap[".ami"] = "application/vnd.amiga.ami"
	DefaultMimeMap[".cii"] = "application/vnd.anser-web-certificate-issue-initiation"
	DefaultMimeMap[".fti"] = "application/vnd.anser-web-funds-transfer-initiation"
	DefaultMimeMap[".atx"] = "application/vnd.antix.game-component"
	DefaultMimeMap[".mpkg"] = "application/vnd.apple.installer+xml"
	DefaultMimeMap[".aep"] = "application/vnd.audiograph"
	DefaultMimeMap[".mpm"] = "application/vnd.blueice.multipass"
	DefaultMimeMap[".bmi"] = "application/vnd.bmi"
	DefaultMimeMap[".rep"] = "application/vnd.businessobjects"
	DefaultMimeMap[".cdxml"] = "application/vnd.chemdraw+xml"
	DefaultMimeMap[".mmd"] = "application/vnd.chipnuts.karaoke-mmd"
	DefaultMimeMap[".cdy"] = "application/vnd.cinderella"
	DefaultMimeMap[".cla"] = "application/vnd.claymore"
	DefaultMimeMap[".c4g"] = "application/vnd.clonk.c4group"
	DefaultMimeMap[".c4d"] = "application/vnd.clonk.c4group"
	DefaultMimeMap[".c4f"] = "application/vnd.clonk.c4group"
	DefaultMimeMap[".c4p"] = "application/vnd.clonk.c4group"
	DefaultMimeMap[".c4u"] = "application/vnd.clonk.c4group"
	DefaultMimeMap[".csp"] = "application/vnd.commonspace"
	DefaultMimeMap[".cst"] = "application/vnd.commonspace"
	DefaultMimeMap[".cdbcmsg"] = "application/vnd.contact.cmsg"
	DefaultMimeMap[".cmc"] = "application/vnd.cosmocaller"
	DefaultMimeMap[".clkx"] = "application/vnd.crick.clicker"
	DefaultMimeMap[".clkk"] = "application/vnd.crick.clicker.keyboard"
	DefaultMimeMap[".clkp"] = "application/vnd.crick.clicker.palette"
	DefaultMimeMap[".clkt"] = "application/vnd.crick.clicker.template"
	DefaultMimeMap[".clkw"] = "application/vnd.crick.clicker.wordbank"
	DefaultMimeMap[".wbs"] = "application/vnd.criticaltools.wbs+xml"
	DefaultMimeMap[".pml"] = "application/vnd.ctc-posml"
	DefaultMimeMap[".ppd"] = "application/vnd.cups-ppd"
	DefaultMimeMap[".curl"] = "application/vnd.curl"
	DefaultMimeMap[".rdz"] = "application/vnd.data-vision.rdz"
	DefaultMimeMap[".fe_launch"] = "application/vnd.denovo.fcselayout-link"
	DefaultMimeMap[".dna"] = "application/vnd.dna"
	DefaultMimeMap[".mlp"] = "application/vnd.dolby.mlp"
	DefaultMimeMap[".dpg"] = "application/vnd.dpgraph"
	DefaultMimeMap[".dfac"] = "application/vnd.dreamfactory"
	DefaultMimeMap[".mag"] = "application/vnd.ecowin.chart"
	DefaultMimeMap[".nml"] = "application/vnd.enliven"
	DefaultMimeMap[".esf"] = "application/vnd.epson.esf"
	DefaultMimeMap[".msf"] = "application/vnd.epson.msf"
	DefaultMimeMap[".qam"] = "application/vnd.epson.quickanime"
	DefaultMimeMap[".slt"] = "application/vnd.epson.salt"
	DefaultMimeMap[".ssf"] = "application/vnd.epson.ssf"
	DefaultMimeMap[".es3"] = "application/vnd.eszigno3+xml"
	DefaultMimeMap[".et3"] = "application/vnd.eszigno3+xml"
	DefaultMimeMap[".ez2"] = "application/vnd.ezpix-album"
	DefaultMimeMap[".ez3"] = "application/vnd.ezpix-package"
	DefaultMimeMap[".fdf"] = "application/vnd.fdf"
	DefaultMimeMap[".gph"] = "application/vnd.flographit"
	DefaultMimeMap[".ftc"] = "application/vnd.fluxtime.clip"
	DefaultMimeMap[".fm"] = "application/vnd.framemaker"
	DefaultMimeMap[".frame"] = "application/vnd.framemaker"
	DefaultMimeMap[".maker"] = "application/vnd.framemaker"
	DefaultMimeMap[".fnc"] = "application/vnd.frogans.fnc"
	DefaultMimeMap[".ltf"] = "application/vnd.frogans.ltf"
	DefaultMimeMap[".fsc"] = "application/vnd.fsc.weblaunch"
	DefaultMimeMap[".oas"] = "application/vnd.fujitsu.oasys"
	DefaultMimeMap[".oa2"] = "application/vnd.fujitsu.oasys2"
	DefaultMimeMap[".oa3"] = "application/vnd.fujitsu.oasys3"
	DefaultMimeMap[".fg5"] = "application/vnd.fujitsu.oasysgp"
	DefaultMimeMap[".bh2"] = "application/vnd.fujitsu.oasysprs"
	DefaultMimeMap[".ddd"] = "application/vnd.fujixerox.ddd"
	DefaultMimeMap[".xdw"] = "application/vnd.fujixerox.docuworks"
	DefaultMimeMap[".xbd"] = "application/vnd.fujixerox.docuworks.binder"
	DefaultMimeMap[".fzs"] = "application/vnd.fuzzysheet"
	DefaultMimeMap[".txd"] = "application/vnd.genomatix.tuxedo"
	DefaultMimeMap[".kml"] = "application/vnd.google-earth.kml+xml"
	DefaultMimeMap[".kmz"] = "application/vnd.google-earth.kmz"
	DefaultMimeMap[".gqf"] = "application/vnd.grafeq"
	DefaultMimeMap[".gqs"] = "application/vnd.grafeq"
	DefaultMimeMap[".gac"] = "application/vnd.groove-account"
	DefaultMimeMap[".ghf"] = "application/vnd.groove-help"
	DefaultMimeMap[".gim"] = "application/vnd.groove-identity-message"
	DefaultMimeMap[".grv"] = "application/vnd.groove-injector"
	DefaultMimeMap[".gtm"] = "application/vnd.groove-tool-message"
	DefaultMimeMap[".tpl"] = "application/vnd.groove-tool-template"
	DefaultMimeMap[".vcg"] = "application/vnd.groove-vcard"
	DefaultMimeMap[".zmm"] = "application/vnd.handheld-entertainment+xml"
	DefaultMimeMap[".hbci"] = "application/vnd.hbci"
	DefaultMimeMap[".les"] = "application/vnd.hhe.lesson-player"
	DefaultMimeMap[".hpgl"] = "application/vnd.hp-hpgl"
	DefaultMimeMap[".hpid"] = "application/vnd.hp-hpid"
	DefaultMimeMap[".hps"] = "application/vnd.hp-hps"
	DefaultMimeMap[".jlt"] = "application/vnd.hp-jlyt"
	DefaultMimeMap[".pcl"] = "application/vnd.hp-pcl"
	DefaultMimeMap[".pclxl"] = "application/vnd.hp-pclxl"
	DefaultMimeMap[".x3d"] = "application/vnd.hzn-3d-crossword"
	DefaultMimeMap[".mpy"] = "application/vnd.ibm.minipay"
	DefaultMimeMap[".afp"] = "application/vnd.ibm.modcap"
	DefaultMimeMap[".listafp"] = "application/vnd.ibm.modcap"
	DefaultMimeMap[".list3820"] = "application/vnd.ibm.modcap"
	DefaultMimeMap[".irm"] = "application/vnd.ibm.rights-management"
	DefaultMimeMap[".sc"] = "application/vnd.ibm.secure-container"
	DefaultMimeMap[".igl"] = "application/vnd.igloader"
	DefaultMimeMap[".ivp"] = "application/vnd.immervision-ivp"
	DefaultMimeMap[".ivu"] = "application/vnd.immervision-ivu"
	DefaultMimeMap[".xpw"] = "application/vnd.intercon.formnet"
	DefaultMimeMap[".xpx"] = "application/vnd.intercon.formnet"
	DefaultMimeMap[".qbo"] = "application/vnd.intu.qbo"
	DefaultMimeMap[".qfx"] = "application/vnd.intu.qfx"
	DefaultMimeMap[".rcprofile"] = "application/vnd.ipunplugged.rcprofile"
	DefaultMimeMap[".irp"] = "application/vnd.irepository.package+xml"
	DefaultMimeMap[".xpr"] = "application/vnd.is-xpr"
	DefaultMimeMap[".jam"] = "application/vnd.jam"
	DefaultMimeMap[".rms"] = "application/vnd.jcp.javame.midlet-rms"
	DefaultMimeMap[".jisp"] = "application/vnd.jisp"
	DefaultMimeMap[".joda"] = "application/vnd.joost.joda-archive"
	DefaultMimeMap[".ktz"] = "application/vnd.kahootz"
	DefaultMimeMap[".ktr"] = "application/vnd.kahootz"
	DefaultMimeMap[".karbon"] = "application/vnd.kde.karbon"
	DefaultMimeMap[".chrt"] = "application/vnd.kde.kchart"
	DefaultMimeMap[".kfo"] = "application/vnd.kde.kformula"
	DefaultMimeMap[".flw"] = "application/vnd.kde.kivio"
	DefaultMimeMap[".kon"] = "application/vnd.kde.kontour"
	DefaultMimeMap[".kpr"] = "application/vnd.kde.kpresenter"
	DefaultMimeMap[".kpt"] = "application/vnd.kde.kpresenter"
	DefaultMimeMap[".ksp"] = "application/vnd.kde.kspread"
	DefaultMimeMap[".kwd"] = "application/vnd.kde.kword"
	DefaultMimeMap[".kwt"] = "application/vnd.kde.kword"
	DefaultMimeMap[".htke"] = "application/vnd.kenameaapp"
	DefaultMimeMap[".kia"] = "application/vnd.kidspiration"
	DefaultMimeMap[".kne"] = "application/vnd.kinar"
	DefaultMimeMap[".knp"] = "application/vnd.kinar"
	DefaultMimeMap[".skp"] = "application/vnd.koan"
	DefaultMimeMap[".skd"] = "application/vnd.koan"
	DefaultMimeMap[".skt"] = "application/vnd.koan"
	DefaultMimeMap[".skm"] = "application/vnd.koan"
	DefaultMimeMap[".lbd"] = "application/vnd.llamagraphics.life-balance.desktop"
	DefaultMimeMap[".lbe"] = "application/vnd.llamagraphics.life-balance.exchange+xml"
	DefaultMimeMap[".123"] = "application/vnd.lotus-1-2-3"
	DefaultMimeMap[".apr"] = "application/vnd.lotus-approach"
	DefaultMimeMap[".pre"] = "application/vnd.lotus-freelance"
	DefaultMimeMap[".nsf"] = "application/vnd.lotus-notes"
	DefaultMimeMap[".org"] = "application/vnd.lotus-organizer"
	DefaultMimeMap[".scm"] = "application/vnd.lotus-screencam"
	DefaultMimeMap[".lwp"] = "application/vnd.lotus-wordpro"
	DefaultMimeMap[".portpkg"] = "application/vnd.macports.portpkg"
	DefaultMimeMap[".mcd"] = "application/vnd.mcd"
	DefaultMimeMap[".mc1"] = "application/vnd.medcalcdata"
	DefaultMimeMap[".cdkey"] = "application/vnd.mediastation.cdkey"
	DefaultMimeMap[".mwf"] = "application/vnd.mfer"
	DefaultMimeMap[".mfm"] = "application/vnd.mfmp"
	DefaultMimeMap[".flo"] = "application/vnd.micrografx.flo"
	DefaultMimeMap[".igx"] = "application/vnd.micrografx.igx"
	DefaultMimeMap[".mif"] = "application/vnd.mif"
	DefaultMimeMap[".daf"] = "application/vnd.mobius.daf"
	DefaultMimeMap[".dis"] = "application/vnd.mobius.dis"
	DefaultMimeMap[".mbk"] = "application/vnd.mobius.mbk"
	DefaultMimeMap[".mqy"] = "application/vnd.mobius.mqy"
	DefaultMimeMap[".msl"] = "application/vnd.mobius.msl"
	DefaultMimeMap[".plc"] = "application/vnd.mobius.plc"
	DefaultMimeMap[".txf"] = "application/vnd.mobius.txf"
	DefaultMimeMap[".mpn"] = "application/vnd.mophun.application"
	DefaultMimeMap[".mpc"] = "application/vnd.mophun.certificate"
	DefaultMimeMap[".cil"] = "application/vnd.ms-artgalry"
	DefaultMimeMap[".asf"] = "application/vnd.ms-asf"
	DefaultMimeMap[".cab"] = "application/vnd.ms-cab-compressed"
	DefaultMimeMap[".xls"] = "application/vnd.ms-excel"
	DefaultMimeMap[".xlm"] = "application/vnd.ms-excel"
	DefaultMimeMap[".xla"] = "application/vnd.ms-excel"
	DefaultMimeMap[".xlc"] = "application/vnd.ms-excel"
	DefaultMimeMap[".xlt"] = "application/vnd.ms-excel"
	DefaultMimeMap[".xlw"] = "application/vnd.ms-excel"
	DefaultMimeMap[".eot"] = "application/vnd.ms-fontobject"
	DefaultMimeMap[".chm"] = "application/vnd.ms-htmlhelp"
	DefaultMimeMap[".ims"] = "application/vnd.ms-ims"
	DefaultMimeMap[".lrm"] = "application/vnd.ms-lrm"
	DefaultMimeMap[".ppt"] = "application/vnd.ms-powerpoint"
	DefaultMimeMap[".pps"] = "application/vnd.ms-powerpoint"
	DefaultMimeMap[".pot"] = "application/vnd.ms-powerpoint"
	DefaultMimeMap[".mpp"] = "application/vnd.ms-project"
	DefaultMimeMap[".mpt"] = "application/vnd.ms-project"
	DefaultMimeMap[".wps"] = "application/vnd.ms-works"
	DefaultMimeMap[".wks"] = "application/vnd.ms-works"
	DefaultMimeMap[".wcm"] = "application/vnd.ms-works"
	DefaultMimeMap[".wdb"] = "application/vnd.ms-works"
	DefaultMimeMap[".wpl"] = "application/vnd.ms-wpl"
	DefaultMimeMap[".xps"] = "application/vnd.ms-xpsdocument"
	DefaultMimeMap[".mseq"] = "application/vnd.mseq"
	DefaultMimeMap[".mus"] = "application/vnd.musician"
	DefaultMimeMap[".msty"] = "application/vnd.muvee.style"
	DefaultMimeMap[".nlu"] = "application/vnd.neurolanguage.nlu"
	DefaultMimeMap[".nnd"] = "application/vnd.noblenet-directory"
	DefaultMimeMap[".nns"] = "application/vnd.noblenet-sealer"
	DefaultMimeMap[".nnw"] = "application/vnd.noblenet-web"
	DefaultMimeMap[".ngdat"] = "application/vnd.nokia.n-gage.data"
	DefaultMimeMap[".n-gage"] = "application/vnd.nokia.n-gage.symbian.install"
	DefaultMimeMap[".rpst"] = "application/vnd.nokia.radio-preset"
	DefaultMimeMap[".rpss"] = "application/vnd.nokia.radio-presets"
	DefaultMimeMap[".edm"] = "application/vnd.novadigm.edm"
	DefaultMimeMap[".edx"] = "application/vnd.novadigm.edx"
	DefaultMimeMap[".ext"] = "application/vnd.novadigm.ext"
	DefaultMimeMap[".odc"] = "application/vnd.oasis.opendocument.chart"
	DefaultMimeMap[".otc"] = "application/vnd.oasis.opendocument.chart-template"
	DefaultMimeMap[".odf"] = "application/vnd.oasis.opendocument.formula"
	DefaultMimeMap[".otf"] = "application/vnd.oasis.opendocument.formula-template"
	DefaultMimeMap[".odg"] = "application/vnd.oasis.opendocument.graphics"
	DefaultMimeMap[".otg"] = "application/vnd.oasis.opendocument.graphics-template"
	DefaultMimeMap[".odi"] = "application/vnd.oasis.opendocument.image"
	DefaultMimeMap[".oti"] = "application/vnd.oasis.opendocument.image-template"
	DefaultMimeMap[".odp"] = "application/vnd.oasis.opendocument.presentation"
	DefaultMimeMap[".otp"] = "application/vnd.oasis.opendocument.presentation-template"
	DefaultMimeMap[".ods"] = "application/vnd.oasis.opendocument.spreadsheet"
	DefaultMimeMap[".ots"] = "application/vnd.oasis.opendocument.spreadsheet-template"
	DefaultMimeMap[".odt"] = "application/vnd.oasis.opendocument.text"
	DefaultMimeMap[".otm"] = "application/vnd.oasis.opendocument.text-master"
	DefaultMimeMap[".ott"] = "application/vnd.oasis.opendocument.text-template"
	DefaultMimeMap[".oth"] = "application/vnd.oasis.opendocument.text-web"
	DefaultMimeMap[".xo"] = "application/vnd.olpc-sugar"
	DefaultMimeMap[".dd2"] = "application/vnd.oma.dd2+xml"
	DefaultMimeMap[".oxt"] = "application/vnd.openofficeorg.extension"
	DefaultMimeMap[".dp"] = "application/vnd.osgi.dp"
	DefaultMimeMap[".prc"] = "application/vnd.palm"
	DefaultMimeMap[".pdb"] = "application/vnd.palm"
	DefaultMimeMap[".pqa"] = "application/vnd.palm"
	DefaultMimeMap[".oprc"] = "application/vnd.palm"
	DefaultMimeMap[".str"] = "application/vnd.pg.format"
	DefaultMimeMap[".ei6"] = "application/vnd.pg.osasli"
	DefaultMimeMap[".efif"] = "application/vnd.picsel"
	DefaultMimeMap[".plf"] = "application/vnd.pocketlearn"
	DefaultMimeMap[".pbd"] = "application/vnd.powerbuilder6"
	DefaultMimeMap[".box"] = "application/vnd.previewsystems.box"
	DefaultMimeMap[".mgz"] = "application/vnd.proteus.magazine"
	DefaultMimeMap[".qps"] = "application/vnd.publishare-delta-tree"
	DefaultMimeMap[".ptid"] = "application/vnd.pvi.ptid1"
	DefaultMimeMap[".qxd"] = "application/vnd.quark.quarkxpress"
	DefaultMimeMap[".qxt"] = "application/vnd.quark.quarkxpress"
	DefaultMimeMap[".qwd"] = "application/vnd.quark.quarkxpress"
	DefaultMimeMap[".qwt"] = "application/vnd.quark.quarkxpress"
	DefaultMimeMap[".qxl"] = "application/vnd.quark.quarkxpress"
	DefaultMimeMap[".qxb"] = "application/vnd.quark.quarkxpress"
	DefaultMimeMap[".mxl"] = "application/vnd.recordare.musicxml"
	DefaultMimeMap[".rm"] = "application/vnd.rn-realmedia"
	DefaultMimeMap[".see"] = "application/vnd.seemail"
	DefaultMimeMap[".sema"] = "application/vnd.sema"
	DefaultMimeMap[".semd"] = "application/vnd.semd"
	DefaultMimeMap[".semf"] = "application/vnd.semf"
	DefaultMimeMap[".ifm"] = "application/vnd.shana.informed.formdata"
	DefaultMimeMap[".itp"] = "application/vnd.shana.informed.formtemplate"
	DefaultMimeMap[".iif"] = "application/vnd.shana.informed.interchange"
	DefaultMimeMap[".ipk"] = "application/vnd.shana.informed.package"
	DefaultMimeMap[".twd"] = "application/vnd.simtech-mindmapper"
	DefaultMimeMap[".twds"] = "application/vnd.simtech-mindmapper"
	DefaultMimeMap[".mmf"] = "application/vnd.smaf"
	DefaultMimeMap[".sdkm"] = "application/vnd.solent.sdkm+xml"
	DefaultMimeMap[".sdkd"] = "application/vnd.solent.sdkm+xml"
	DefaultMimeMap[".dxp"] = "application/vnd.spotfire.dxp"
	DefaultMimeMap[".sfs"] = "application/vnd.spotfire.sfs"
	DefaultMimeMap[".sus"] = "application/vnd.sus-calendar"
	DefaultMimeMap[".susp"] = "application/vnd.sus-calendar"
	DefaultMimeMap[".svd"] = "application/vnd.svd"
	DefaultMimeMap[".xsm"] = "application/vnd.syncml+xml"
	DefaultMimeMap[".bdm"] = "application/vnd.syncml.dm+wbxml"
	DefaultMimeMap[".xdm"] = "application/vnd.syncml.dm+xml"
	DefaultMimeMap[".tao"] = "application/vnd.tao.intent-module-archive"
	DefaultMimeMap[".tmo"] = "application/vnd.tmobile-livetv"
	DefaultMimeMap[".tpt"] = "application/vnd.trid.tpt"
	DefaultMimeMap[".mxs"] = "application/vnd.triscape.mxs"
	DefaultMimeMap[".tra"] = "application/vnd.trueapp"
	DefaultMimeMap[".ufd"] = "application/vnd.ufdl"
	DefaultMimeMap[".ufdl"] = "application/vnd.ufdl"
	DefaultMimeMap[".utz"] = "application/vnd.uiq.theme"
	DefaultMimeMap[".umj"] = "application/vnd.umajin"
	DefaultMimeMap[".unityweb"] = "application/vnd.unity"
	DefaultMimeMap[".uoml"] = "application/vnd.uoml+xml"
	DefaultMimeMap[".vcx"] = "application/vnd.vcx"
	DefaultMimeMap[".vsd"] = "application/vnd.visio"
	DefaultMimeMap[".vst"] = "application/vnd.visio"
	DefaultMimeMap[".vss"] = "application/vnd.visio"
	DefaultMimeMap[".vsw"] = "application/vnd.visio"
	DefaultMimeMap[".vis"] = "application/vnd.visionary"
	DefaultMimeMap[".vsf"] = "application/vnd.vsf"
	DefaultMimeMap[".wbxml"] = "application/vnd.wap.wbxml"
	DefaultMimeMap[".wmlc"] = "application/vnd.wap.wmlc"
	DefaultMimeMap[".wmlsc"] = "application/vnd.wap.wmlscriptc"
	DefaultMimeMap[".wtb"] = "application/vnd.webturbo"
	DefaultMimeMap[".wpd"] = "application/vnd.wordperfect"
	DefaultMimeMap[".wqd"] = "application/vnd.wqd"
	DefaultMimeMap[".stf"] = "application/vnd.wt.stf"
	DefaultMimeMap[".xar"] = "application/vnd.xara"
	DefaultMimeMap[".xfdl"] = "application/vnd.xfdl"
	DefaultMimeMap[".hvd"] = "application/vnd.yamaha.hv-dic"
	DefaultMimeMap[".hvs"] = "application/vnd.yamaha.hv-script"
	DefaultMimeMap[".hvp"] = "application/vnd.yamaha.hv-voice"
	DefaultMimeMap[".saf"] = "application/vnd.yamaha.smaf-audio"
	DefaultMimeMap[".spf"] = "application/vnd.yamaha.smaf-phrase"
	DefaultMimeMap[".cmp"] = "application/vnd.yellowriver-custom-menu"
	DefaultMimeMap[".zaz"] = "application/vnd.zzazz.deck+xml"
	DefaultMimeMap[".vxml"] = "application/voicexml+xml"
	DefaultMimeMap[".hlp"] = "application/winhlp"
	DefaultMimeMap[".wsdl"] = "application/wsdl+xml"
	DefaultMimeMap[".wspolicy"] = "application/wspolicy+xml"
	DefaultMimeMap[".ace"] = "application/x-ace-compressed"
	DefaultMimeMap[".bcpio"] = "application/x-bcpio"
	DefaultMimeMap[".torrent"] = "application/x-bittorrent"
	DefaultMimeMap[".bz"] = "application/x-bzip"
	DefaultMimeMap[".bz2"] = "application/x-bzip2"
	DefaultMimeMap[".boz"] = "application/x-bzip2"
	DefaultMimeMap[".vcd"] = "application/x-cdlink"
	DefaultMimeMap[".chat"] = "application/x-chat"
	DefaultMimeMap[".pgn"] = "application/x-chess-pgn"
	DefaultMimeMap[".cpio"] = "application/x-cpio"
	DefaultMimeMap[".csh"] = "application/x-csh"
	DefaultMimeMap[".dcr"] = "application/x-director"
	DefaultMimeMap[".dir"] = "application/x-director"
	DefaultMimeMap[".dxr"] = "application/x-director"
	DefaultMimeMap[".fgd"] = "application/x-director"
	DefaultMimeMap[".dvi"] = "application/x-dvi"
	DefaultMimeMap[".spl"] = "application/x-futuresplash"
	DefaultMimeMap[".gtar"] = "application/x-gtar"
	DefaultMimeMap[".hdf"] = "application/x-hdf"
	DefaultMimeMap[".latex"] = "application/x-latex"
	DefaultMimeMap[".wmd"] = "application/x-ms-wmd"
	DefaultMimeMap[".wmz"] = "application/x-ms-wmz"
	DefaultMimeMap[".mdb"] = "application/x-msaccess"
	DefaultMimeMap[".obd"] = "application/x-msbinder"
	DefaultMimeMap[".crd"] = "application/x-mscardfile"
	DefaultMimeMap[".clp"] = "application/x-msclip"
	DefaultMimeMap[".exe"] = "application/x-msdownload"
	DefaultMimeMap[".dll"] = "application/x-msdownload"
	DefaultMimeMap[".com"] = "application/x-msdownload"
	DefaultMimeMap[".bat"] = "application/x-msdownload"
	DefaultMimeMap[".msi"] = "application/x-msdownload"
	DefaultMimeMap[".mvb"] = "application/x-msmediaview"
	DefaultMimeMap[".m13"] = "application/x-msmediaview"
	DefaultMimeMap[".m14"] = "application/x-msmediaview"
	DefaultMimeMap[".wmf"] = "application/x-msmetafile"
	DefaultMimeMap[".mny"] = "application/x-msmoney"
	DefaultMimeMap[".pub"] = "application/x-mspublisher"
	DefaultMimeMap[".scd"] = "application/x-msschedule"
	DefaultMimeMap[".trm"] = "application/x-msterminal"
	DefaultMimeMap[".wri"] = "application/x-mswrite"
	DefaultMimeMap[".nc"] = "application/x-netcdf"
	DefaultMimeMap[".cdf"] = "application/x-netcdf"
	DefaultMimeMap[".p12"] = "application/x-pkcs12"
	DefaultMimeMap[".pfx"] = "application/x-pkcs12"
	DefaultMimeMap[".p7b"] = "application/x-pkcs7-certificates"
	DefaultMimeMap[".spc"] = "application/x-pkcs7-certificates"
	DefaultMimeMap[".p7r"] = "application/x-pkcs7-certreqresp"
	DefaultMimeMap[".rar"] = "application/x-rar-compressed"
	DefaultMimeMap[".sh"] = "application/x-sh"
	DefaultMimeMap[".shar"] = "application/x-shar"
	DefaultMimeMap[".swf"] = "application/x-shockwave-flash"
	DefaultMimeMap[".sit"] = "application/x-stuffit"
	DefaultMimeMap[".sitx"] = "application/x-stuffitx"
	DefaultMimeMap[".sv4cpio"] = "application/x-sv4cpio"
	DefaultMimeMap[".sv4crc"] = "application/x-sv4crc"
	DefaultMimeMap[".tar"] = "application/x-tar"
	DefaultMimeMap[".tcl"] = "application/x-tcl"
	DefaultMimeMap[".tex"] = "application/x-tex"
	DefaultMimeMap[".texinfo"] = "application/x-texinfo"
	DefaultMimeMap[".texi"] = "application/x-texinfo"
	DefaultMimeMap[".ustar"] = "application/x-ustar"
	DefaultMimeMap[".src"] = "application/x-wais-source"
	DefaultMimeMap[".der"] = "application/x-x509-ca-cert"
	DefaultMimeMap[".crt"] = "application/x-x509-ca-cert"
	DefaultMimeMap[".xenc"] = "application/xenc+xml"
	DefaultMimeMap[".xhtml"] = "application/xhtml+xml"
	DefaultMimeMap[".xht"] = "application/xhtml+xml"
	DefaultMimeMap[".xml"] = "text/xml"
	DefaultMimeMap[".xsl"] = "application/xml"
	DefaultMimeMap[".dtd"] = "application/xml-dtd"
	DefaultMimeMap[".xop"] = "application/xop+xml"
	DefaultMimeMap[".xslt"] = "application/xslt+xml"
	DefaultMimeMap[".xspf"] = "application/xspf+xml"
	DefaultMimeMap[".mxml"] = "application/xv+xml"
	DefaultMimeMap[".xhvml"] = "application/xv+xml"
	DefaultMimeMap[".xvml"] = "application/xv+xml"
	DefaultMimeMap[".xvm"] = "application/xv+xml"
	DefaultMimeMap[".zip"] = "application/zip"
	DefaultMimeMap[".au"] = "audio/basic"
	DefaultMimeMap[".snd"] = "audio/basic"
	DefaultMimeMap[".mid"] = "audio/midi"
	DefaultMimeMap[".midi"] = "audio/midi"
	DefaultMimeMap[".kar"] = "audio/midi"
	DefaultMimeMap[".rmi"] = "audio/midi"
	DefaultMimeMap[".mp4a"] = "audio/mp4"
	DefaultMimeMap[".mpga"] = "audio/mpeg"
	DefaultMimeMap[".mp2"] = "audio/mpeg"
	DefaultMimeMap[".mp2a"] = "audio/mpeg"
	DefaultMimeMap[".mp3"] = "audio/mpeg"
	DefaultMimeMap[".m2a"] = "audio/mpeg"
	DefaultMimeMap[".m3a"] = "audio/mpeg"
	DefaultMimeMap[".eol"] = "audio/vnd.digital-winds"
	DefaultMimeMap[".lvp"] = "audio/vnd.lucent.voice"
	DefaultMimeMap[".ecelp4800"] = "audio/vnd.nuera.ecelp4800"
	DefaultMimeMap[".ecelp7470"] = "audio/vnd.nuera.ecelp7470"
	DefaultMimeMap[".ecelp9600"] = "audio/vnd.nuera.ecelp9600"
	DefaultMimeMap[".wav"] = "audio/wav"
	DefaultMimeMap[".aif"] = "audio/x-aiff"
	DefaultMimeMap[".aiff"] = "audio/x-aiff"
	DefaultMimeMap[".aifc"] = "audio/x-aiff"
	DefaultMimeMap[".m3u"] = "audio/x-mpegurl"
	DefaultMimeMap[".wax"] = "audio/x-ms-wax"
	DefaultMimeMap[".wma"] = "audio/x-ms-wma"
	DefaultMimeMap[".ram"] = "audio/x-pn-realaudio"
	DefaultMimeMap[".ra"] = "audio/x-pn-realaudio"
	DefaultMimeMap[".rmp"] = "audio/x-pn-realaudio-plugin"
	DefaultMimeMap[".cdx"] = "chemical/x-cdx"
	DefaultMimeMap[".cif"] = "chemical/x-cif"
	DefaultMimeMap[".cmdf"] = "chemical/x-cmdf"
	DefaultMimeMap[".cml"] = "chemical/x-cml"
	DefaultMimeMap[".csml"] = "chemical/x-csml"
	DefaultMimeMap[".xyz"] = "chemical/x-xyz"
	DefaultMimeMap[".bmp"] = "image/bmp"
	DefaultMimeMap[".cgm"] = "image/cgm"
	DefaultMimeMap[".g3"] = "image/g3fax"
	DefaultMimeMap[".gif"] = "image/gif"
	DefaultMimeMap[".ief"] = "image/ief"
	DefaultMimeMap[".jpeg"] = "image/jpeg"
	DefaultMimeMap[".jpg"] = "image/jpeg"
	DefaultMimeMap[".jpe"] = "image/jpeg"
	DefaultMimeMap[".png"] = "image/png"
	DefaultMimeMap[".webp"] = "image/webp"
	DefaultMimeMap[".heic"] = "image/heic"
	DefaultMimeMap[".btif"] = "image/prs.btif"
	DefaultMimeMap[".svg"] = "image/svg+xml"
	DefaultMimeMap[".svgz"] = "image/svg+xml"
	DefaultMimeMap[".tiff"] = "image/tiff"
	DefaultMimeMap[".tif"] = "image/tiff"
	DefaultMimeMap[".psd"] = "image/vnd.adobe.photoshop"
	DefaultMimeMap[".djvu"] = "image/vnd.djvu"
	DefaultMimeMap[".djv"] = "image/vnd.djvu"
	DefaultMimeMap[".dwg"] = "image/vnd.dwg"
	DefaultMimeMap[".dxf"] = "image/vnd.dxf"
	DefaultMimeMap[".fbs"] = "image/vnd.fastbidsheet"
	DefaultMimeMap[".fpx"] = "image/vnd.fpx"
	DefaultMimeMap[".fst"] = "image/vnd.fst"
	DefaultMimeMap[".mmr"] = "image/vnd.fujixerox.edmics-mmr"
	DefaultMimeMap[".rlc"] = "image/vnd.fujixerox.edmics-rlc"
	DefaultMimeMap[".mdi"] = "image/vnd.ms-modi"
	DefaultMimeMap[".npx"] = "image/vnd.net-fpx"
	DefaultMimeMap[".wbmp"] = "image/vnd.wap.wbmp"
	DefaultMimeMap[".xif"] = "image/vnd.xiff"
	DefaultMimeMap[".ras"] = "image/x-cmu-raster"
	DefaultMimeMap[".cmx"] = "image/x-cmx"
	DefaultMimeMap[".ico"] = "image/x-icon"
	DefaultMimeMap[".pcx"] = "image/x-pcx"
	DefaultMimeMap[".pic"] = "image/x-pict"
	DefaultMimeMap[".pct"] = "image/x-pict"
	DefaultMimeMap[".pnm"] = "image/x-portable-anymap"
	DefaultMimeMap[".pbm"] = "image/x-portable-bitmap"
	DefaultMimeMap[".pgm"] = "image/x-portable-graymap"
	DefaultMimeMap[".ppm"] = "image/x-portable-pixmap"
	DefaultMimeMap[".rgb"] = "image/x-rgb"
	DefaultMimeMap[".xbm"] = "image/x-xbitmap"
	DefaultMimeMap[".xpm"] = "image/x-xpixmap"
	DefaultMimeMap[".xwd"] = "image/x-xwindowdump"
	DefaultMimeMap[".eml"] = "message/rfc822"
	DefaultMimeMap[".mime"] = "message/rfc822"
	DefaultMimeMap[".mht"] = "message/rfc822"
	DefaultMimeMap[".mhtml"] = "message/rfc822"
	DefaultMimeMap[".igs"] = "model/iges"
	DefaultMimeMap[".iges"] = "model/iges"
	DefaultMimeMap[".msh"] = "model/mesh"
	DefaultMimeMap[".mesh"] = "model/mesh"
	DefaultMimeMap[".silo"] = "model/mesh"
	DefaultMimeMap[".dwf"] = "model/vnd.dwf"
	DefaultMimeMap[".gdl"] = "model/vnd.gdl"
	DefaultMimeMap[".gtw"] = "model/vnd.gtw"
	DefaultMimeMap[".mts"] = "model/vnd.mts"
	DefaultMimeMap[".vtu"] = "model/vnd.vtu"
	DefaultMimeMap[".wrl"] = "model/vrml"
	DefaultMimeMap[".vrml"] = "model/vrml"
	DefaultMimeMap[".ics"] = "text/calendar"
	DefaultMimeMap[".ifb"] = "text/calendar"
	DefaultMimeMap[".css"] = "text/css"
	DefaultMimeMap[".csv"] = "text/csv"
	DefaultMimeMap[".html"] = "text/html"
	DefaultMimeMap[".htm"] = "text/html"
	DefaultMimeMap[".txt"] = "text/plain"
	DefaultMimeMap[".text"] = "text/plain"
	DefaultMimeMap[".conf"] = "text/plain"
	DefaultMimeMap[".def"] = "text/plain"
	DefaultMimeMap[".list"] = "text/plain"
	DefaultMimeMap[".log"] = "text/plain"
	DefaultMimeMap[".in"] = "text/plain"
	DefaultMimeMap[".dsc"] = "text/prs.lines.tag"
	DefaultMimeMap[".rtx"] = "text/richtext"
	DefaultMimeMap[".sgml"] = "text/sgml"
	DefaultMimeMap[".sgm"] = "text/sgml"
	DefaultMimeMap[".tsv"] = "text/tab-separated-values"
	DefaultMimeMap[".t"] = "text/troff"
	DefaultMimeMap[".tr"] = "text/troff"
	DefaultMimeMap[".roff"] = "text/troff"
	DefaultMimeMap[".man"] = "text/troff"
	DefaultMimeMap[".me"] = "text/troff"
	DefaultMimeMap[".ms"] = "text/troff"
	DefaultMimeMap[".uri"] = "text/uri-list"
	DefaultMimeMap[".uris"] = "text/uri-list"
	DefaultMimeMap[".urls"] = "text/uri-list"
	DefaultMimeMap[".fly"] = "text/vnd.fly"
	DefaultMimeMap[".flx"] = "text/vnd.fmi.flexstor"
	DefaultMimeMap[".3dml"] = "text/vnd.in3d.3dml"
	DefaultMimeMap[".spot"] = "text/vnd.in3d.spot"
	DefaultMimeMap[".jad"] = "text/vnd.sun.j2me.app-descriptor"
	DefaultMimeMap[".wml"] = "text/vnd.wap.wml"
	DefaultMimeMap[".wmls"] = "text/vnd.wap.wmlscript"
	DefaultMimeMap[".s"] = "text/x-asm"
	DefaultMimeMap[".asm"] = "text/x-asm"
	DefaultMimeMap[".c"] = "text/x-c"
	DefaultMimeMap[".cc"] = "text/x-c"
	DefaultMimeMap[".cxx"] = "text/x-c"
	DefaultMimeMap[".cpp"] = "text/x-c"
	DefaultMimeMap[".h"] = "text/x-c"
	DefaultMimeMap[".hh"] = "text/x-c"
	DefaultMimeMap[".dic"] = "text/x-c"
	DefaultMimeMap[".f"] = "text/x-fortran"
	DefaultMimeMap[".for"] = "text/x-fortran"
	DefaultMimeMap[".f77"] = "text/x-fortran"
	DefaultMimeMap[".f90"] = "text/x-fortran"
	DefaultMimeMap[".p"] = "text/x-pascal"
	DefaultMimeMap[".pas"] = "text/x-pascal"
	DefaultMimeMap[".java"] = "text/x-java-source"
	DefaultMimeMap[".etx"] = "text/x-setext"
	DefaultMimeMap[".uu"] = "text/x-uuencode"
	DefaultMimeMap[".vcs"] = "text/x-vcalendar"
	DefaultMimeMap[".vcf"] = "text/x-vcard"
	DefaultMimeMap[".3gp"] = "video/3gpp"
	DefaultMimeMap[".3g2"] = "video/3gpp2"
	DefaultMimeMap[".h261"] = "video/h261"
	DefaultMimeMap[".h263"] = "video/h263"
	DefaultMimeMap[".h264"] = "video/h264"
	DefaultMimeMap[".jpgv"] = "video/jpeg"
	DefaultMimeMap[".jpm"] = "video/jpm"
	DefaultMimeMap[".jpgm"] = "video/jpm"
	DefaultMimeMap[".mj2"] = "video/mj2"
	DefaultMimeMap[".mjp2"] = "video/mj2"
	DefaultMimeMap[".mp4"] = "video/mp4"
	DefaultMimeMap[".mp4v"] = "video/mp4"
	DefaultMimeMap[".mpg4"] = "video/mp4"
	DefaultMimeMap[".mpeg"] = "video/mpeg"
	DefaultMimeMap[".mpg"] = "video/mpeg"
	DefaultMimeMap[".mpe"] = "video/mpeg"
	DefaultMimeMap[".m1v"] = "video/mpeg"
	DefaultMimeMap[".m2v"] = "video/mpeg"
	DefaultMimeMap[".qt"] = "video/quicktime"
	DefaultMimeMap[".mov"] = "video/quicktime"
	DefaultMimeMap[".fvt"] = "video/vnd.fvt"
	DefaultMimeMap[".mxu"] = "video/vnd.mpegurl"
	DefaultMimeMap[".m4u"] = "video/vnd.mpegurl"
	DefaultMimeMap[".viv"] = "video/vnd.vivo"
	DefaultMimeMap[".fli"] = "video/x-fli"
	DefaultMimeMap[".wm"] = "video/x-ms-wm"
	DefaultMimeMap[".wmv"] = "video/x-ms-wmv"
	DefaultMimeMap[".wmx"] = "video/x-ms-wmx"
	DefaultMimeMap[".wvx"] = "video/x-ms-wvx"
	DefaultMimeMap[".avi"] = "video/x-msvideo"
	DefaultMimeMap[".movie"] = "video/x-sgi-movie"
	DefaultMimeMap[".ice"] = "x-conference/x-cooltalk"
	DefaultMimeMap[".ipa"] = "application/vnd.iphone"
	DefaultMimeMap[".apk"] = "application/vnd.android.package-archive"
}

func GetMimeMap() map[string]string {
	once.Do(LoadMimeMap)
	return DefaultMimeMap
}
