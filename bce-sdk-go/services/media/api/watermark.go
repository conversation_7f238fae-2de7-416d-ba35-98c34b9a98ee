package api

import (
	"encoding/json"
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
)

func CreateWaterMark(cli bce.Client, watermark *CreateWaterMarkArgs) (*CreateWaterMarkResponse, error) {
	req := &bce.BceRequest{}
	req.SetUri(getWatermarkUrl())
	req.SetMethod(http.POST)
	req.SetHeader(http.CONTENT_TYPE, bce.DEFAULT_CONTENT_TYPE)
	jsonBytes, err := json.Marshal(watermark)
	if err != nil {
		return nil, err
	}
	body, err := bce.NewBodyFromBytes(jsonBytes)
	if err != nil {
		return nil, err
	}
	req.SetBody(body)
	resp := &bce.BceResponse{}
	if err := cli.SendRequest(req, resp); err != nil {
		return nil, err
	}
	if resp.<PERSON>Fail() {
		return nil, resp.ServiceError()
	}
	result := &CreateWaterMarkResponse{}
	if err := resp.ParseJsonBody(result); err != nil {
		return nil, err
	}
	return result, nil
}

func GetWaterMark(cli bce.Client, watermarkId string) (*GetWaterMarkResponse, error) {
	req := &bce.BceRequest{}
	req.SetUri(getWatermarkUrl() + "/" + watermarkId)
	req.SetMethod(http.GET)
	resp := &bce.BceResponse{}

	if err := cli.SendRequest(req, resp); err != nil {
		return nil, err
	}
	if resp.IsFail() {
		return nil, resp.ServiceError()
	}
	jsonBody := &GetWaterMarkResponse{}
	if err := resp.ParseJsonBody(jsonBody); err != nil {
		return nil, err
	}
	return jsonBody, nil
}

func ListWaterMark(cli bce.Client) (*ListWaterMarkResponse, error) {
	req := &bce.BceRequest{}
	req.SetUri(getWatermarkUrl())
	req.SetMethod(http.GET)
	resp := &bce.BceResponse{}

	if err := cli.SendRequest(req, resp); err != nil {
		return nil, err
	}
	if resp.IsFail() {
		return nil, resp.ServiceError()
	}
	jsonBody := &ListWaterMarkResponse{}
	if err := resp.ParseJsonBody(jsonBody); err != nil {
		return nil, err
	}
	return jsonBody, nil
}

func DeleteWaterMark(cli bce.Client, watermarkId string) error {
	req := &bce.BceRequest{}
	req.SetUri(getWatermarkUrl() + "/" + watermarkId)
	req.SetMethod(http.DELETE)
	resp := &bce.BceResponse{}
	if err := cli.SendRequest(req, resp); err != nil {
		return err
	}
	if resp.IsFail() {
		return resp.ServiceError()
	}
	return nil
}
