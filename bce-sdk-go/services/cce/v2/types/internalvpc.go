// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/07/28 16:26:00, by <EMAIL>, create
*/

package types

// AvailableZone 可用区
type AvailableZone string

const (
	// ZoneA 可用区 A
	AvailableZoneA AvailableZone = "zoneA"

	// ZoneB 可用区 B
	AvailableZoneB AvailableZone = "zoneB"

	// ZoneC 可用区 C
	AvailableZoneC AvailableZone = "zoneC"

	// ZoneD 可用区 D
	AvailableZoneD AvailableZone = "zoneD"

	// ZoneE 可用区 E
	AvailableZoneE AvailableZone = "zoneE"

	// ZoneF 可用区 F
	AvailableZoneF AvailableZone = "zoneF"
)
