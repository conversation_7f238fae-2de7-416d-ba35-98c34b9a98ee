# CrossVPCEni 解决方案概览

## 1. 方案架构

### 1.1 组件架构

```mermaid
flowchart TD
    subgraph "Pod Network Namespace"
        Pod[Pod]
    end

    subgraph "Host Network"
        CNI[CrossVPC CNI Plugin]
        Agent[CCE Network Agent]
    end

    subgraph "Kubernetes Control Plane"
        CEP[CCEEndpoint]
        ENIC[ENI CR]
        Operator[CrossVPC Operator]
        ENISM[ENI State Machine]
    end

    subgraph "Cloud Platform"
        Cloud[BCE Cloud API]
    end

    Pod --> CNI
    CNI --> Agent
    Agent --> CEP
    Operator --> CEP
    Operator --> ENIC
    ENISM --> ENIC
    ENISM --> Cloud

    style CNI fill:#e3f2fd
    style Agent fill:#fff3e0
    style Operator fill:#e8f5e8
```

### 1.2 模式对比

| 模式 | CNI配置 | 网络实现 | 资源管理 | 删除策略 | 配置来源 |
|------|---------|---------|---------|---------|---------|
| **独占模式** | type=crossvpc-primary | 直接使用ENI设备 | 专用ENI | 立即删除 | CNI配置文件 |
| **共享模式** | type=crossvpc-secondary | 辅助IP配置 | 共享ENI | 延迟删除 | Pod Annotation |

### 1.3 详细文档

- **[独占模式详细方案](./crossvpc-eni-exclusive-mode.md)** - 每个Pod独占一个ENI设备的完整实现方案
- **[共享模式详细方案](./crossvpc-eni-shared-mode.md)** - 多个Pod共享ENI设备的完整实现方案

## 2. 核心流程概览

### 2.1 CNI插件流程

CNI插件根据配置类型（crossvpc-primary/crossvpc-secondary）选择不同的处理模式：

- **独占模式**：直接使用ENI设备，移动到Pod网络命名空间
- **共享模式**：创建veth pair，通过辅助IP实现网络连接

### 2.2 配置获取方式

- **独占模式**：从CNI配置文件中获取crossvpc相关配置
- **共享模式**：从Pod annotation中动态获取配置，CNI配置文件简化

## 3. Agent实现概览

Agent负责处理CNI的IPAM请求，创建和管理CCEEndpoint资源：

1. **ADD流程**：接收CNI请求，创建CEP，等待IP分配完成
2. **DEL流程**：接收CNI请求，删除对应的CEP资源
3. **配置处理**：
   - 独占模式：从CNI配置中获取crossvpc配置
   - 共享模式：从Pod annotation中获取crossvpc配置

## 4. Operator实现概览

Operator负责处理CCEEndpoint资源，根据不同模式执行相应的网络资源分配：

### 4.1 独占模式
- 为每个CEP创建专用的ENI CR
- ENI状态机负责ENI的创建、挂载和管理
- Pod删除时立即删除ENI资源

### 4.2 共享模式
- 查找或创建可复用的共享ENI CR
- 第一个Pod创建ENI并分配辅助IP
- 后续Pod直接在现有ENI上分配辅助IP
- Pod删除时延迟删除ENI资源

## 5. ENI状态机GC流程概览

ENI状态机负责共享模式ENI的垃圾回收：

1. **定时扫描**：每2分钟扫描共享模式的ENI CR
2. **删除条件**：无辅助IP且超过延迟时间
3. **删除策略**：直接标记删除，不再检查CEP使用情况
4. **资源清理**：分离并删除ENI，移除finalizer

## 6. 关键决策流程

### 6.1 CNI插件决策流程

```mermaid
flowchart TD
    A[CNI调用] --> B{解析配置}
    B --> C{模式判断}

    C -->|独占模式| D[查找ENI设备]
    C -->|共享模式| E[创建veth pair]

    D --> F{找到ENI设备?}
    F -->|是| G[移动到容器netns]
    F -->|否| H[返回错误]

    E --> I[配置辅助IP]

    G --> J[配置IP和路由]
    I --> J
    J --> K[返回成功]

    style C fill:#e3f2fd
    style D fill:#fff3e0
    style E fill:#e8f5e8
```

### 6.2 Operator模式选择流程

```mermaid
flowchart TD
    A[CEP事件] --> B{检查类型}

    B -->|CrossVPCPrimary| C[独占模式处理]
    B -->|CrossVPCSecondary| D[共享模式处理]

    C --> E[创建ENI CR]
    E --> F[ENI状态机处理]
    F --> G[更新CEP状态]

    D --> H[查找/创建共享ENI]
    H --> I[分配辅助IP]
    I --> J[更新CEP状态]

    G --> K[完成]
    J --> K

    style B fill:#e3f2fd
    style C fill:#fff3e0
    style D fill:#e8f5e8
```

## 7. 配置示例

### 7.1 独占模式CNI配置

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-network",
  "type": "crossvpc-primary",
  "crossvpc": {
    "userID": "user123",
    "subnetID": "sbn-abc123",
    "securityGroups": ["sg-123456"],
    "vpcCIDR": "***********/16"
  },
  "ipam": {
    "type": "cce-network-agent"
  }
}
```

### 7.2 共享模式CNI配置

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-network",
  "type": "crossvpc-secondary",
  "ipam": {
    "type": "cce-network-agent"
  }
}
```

### 7.3 共享模式Pod配置

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  annotations:
    cce.io/network-type: "crossvpc-secondary"
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456,sg-789012"
spec:
  containers:
  - name: test
    image: nginx
```

### 7.4 Operator配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  crossvpc-eni-enabled: "true"
  crossvpc-eni-exclusive-mode-enabled: "true"
  crossvpc-eni-shared-mode-enabled: "true"
  crossvpc-eni-shared-deletion-delay: "300s"
  crossvpc-eni-gc-interval: "120s"
```

## 8. 总结

CrossVPCEni解决方案提供了两种模式来满足不同的使用场景：

- **独占模式**：适用于对网络性能要求极高的场景，每个Pod独占ENI设备
- **共享模式**：适用于大规模部署场景，通过ENI共享提高资源利用率

两种模式通过统一的架构和组件实现，具有良好的扩展性和可维护性。详细的实现方案请参考对应的模式文档。
