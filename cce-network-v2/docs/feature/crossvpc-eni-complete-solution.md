# CrossVPCEni 完整解决方案

## 1. 方案架构

### 1.1 组件架构

```mermaid
flowchart TD
    subgraph "Pod Network Namespace"
        Pod[Pod]
    end
    
    subgraph "Host Network"
        CNI[CrossVPC CNI Plugin]
        Agent[CCE Network Agent]
    end
    
    subgraph "Kubernetes Control Plane"
        CEP[CCEEndpoint]
        ENIC[ENI CR]
        Operator[CrossVPC Operator]
        ENISM[ENI State Machine]
    end
    
    subgraph "Cloud Platform"
        Cloud[BCE Cloud API]
    end
    
    Pod --> CNI
    CNI --> Agent
    Agent --> CEP
    Operator --> CEP
    Operator --> ENIC
    ENISM --> ENIC
    ENISM --> Cloud
    
    style CNI fill:#e3f2fd
    style Agent fill:#fff3e0
    style Operator fill:#e8f5e8
```

### 1.2 模式对比

| 模式 | CNI配置 | 网络实现 | 资源管理 | 删除策略 |
|------|---------|---------|---------|---------|
| **独占模式** | type=crossvpc-primary | 直接使用ENI设备 | 专用ENI | 立即删除 |
| **共享模式** | type=crossvpc-secondary | 辅助IP配置 | 共享ENI | 延迟删除 |

## 2. CNI插件实现

### 2.1 CNI插件ADD流程

```mermaid
sequenceDiagram
    participant Runtime as Container Runtime
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant Host as Host Network

    Note over Runtime,Host: 【CNI ADD流程】
    
    Runtime->>CNI: CNI ADD调用
    Note right of CNI: 参数：<br/>- netns: /proc/123/ns/net<br/>- ifname: eth0<br/>- config: crossvpc配置
    
    CNI->>CNI: 解析CNI配置
    Note right of CNI: 解析：<br/>- type: crossvpc-primary/secondary<br/>- userID, subnetID<br/>- securityGroups
    
    CNI->>Agent: 请求IP分配
    Note right of Agent: IPAM请求：<br/>POST /ipam/allocate<br/>body: crossvpc配置
    
    Agent->>CNI: 返回网络配置
    Note right of CNI: 响应：<br/>- ip: *************<br/>- gateway: ***********<br/>- interface: eni-abc123 (独占模式)<br/>- mac: 02:00:00:12:34:56 (独占模式)
    
    alt 独占模式
        CNI->>Host: 根据MAC地址查找ENI设备
        CNI->>CNI: 移动ENI设备到容器netns
        CNI->>CNI: 配置IP和路由
    else 共享模式
        CNI->>CNI: 创建veth pair
        CNI->>CNI: 配置辅助IP和路由
    end
    
    CNI->>Runtime: 返回成功结果
```

### 2.2 CNI插件DEL流程

```mermaid
sequenceDiagram
    participant Runtime as Container Runtime
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent

    Note over Runtime,Agent: 【CNI DEL流程】
    
    Runtime->>CNI: CNI DEL调用
    CNI->>CNI: 解析容器ID和网络配置
    
    alt 独占模式
        CNI->>CNI: 移动ENI设备回host netns
        Note right of CNI: ENI设备返回host，等待删除
    else 共享模式
        CNI->>CNI: 删除veth pair
        Note right of CNI: 清理容器网络配置
    end
    
    CNI->>Agent: 请求IP释放
    Note right of Agent: IPAM请求：<br/>POST /ipam/release<br/>body: 容器标识
    
    Agent->>CNI: 返回释放结果
    CNI->>Runtime: 返回成功结果
```

## 3. Agent实现

### 3.1 Agent ADD流程

```mermaid
sequenceDiagram
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant CEP as CCEEndpoint
    participant K8s as Kubernetes API

    Note over CNI,K8s: 【Agent ADD流程】
    
    CNI->>Agent: POST /ipam/allocate
    Note right of Agent: 请求体：<br/>- containerID<br/>- netns<br/>- crossvpcConfig
    
    Agent->>Agent: 查找对应的Pod
    Note right of Agent: 根据containerID<br/>查找Pod信息
    
    Agent->>CEP: 创建CCEEndpoint
    Note right of CEP: spec:<br/>- type: CrossVPCPrimary/Secondary<br/>- crossVPCConfig<br/>- nodeName
    
    Agent->>K8s: 提交CEP到K8s
    K8s-->>Agent: CEP创建成功
    
    loop 等待IP分配 (最多5分钟)
        Agent->>CEP: 查询CEP状态
        CEP-->>Agent: 返回当前状态
        
        alt 分配成功
            Agent->>CNI: 返回网络配置
            Note right of CNI: 响应：<br/>- ip, gateway<br/>- interface, mac (独占模式)
        else 分配失败
            Agent->>CNI: 返回错误信息
        else 分配中
            Agent->>Agent: 等待2秒后重试
        end
    end
```

### 3.2 Agent DEL流程

```mermaid
sequenceDiagram
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant CEP as CCEEndpoint
    participant K8s as Kubernetes API

    Note over CNI,K8s: 【Agent DEL流程】
    
    CNI->>Agent: POST /ipam/release
    Note right of Agent: 请求体：<br/>- containerID<br/>- netns
    
    Agent->>Agent: 查找对应的CEP
    Note right of Agent: 根据containerID<br/>查找CEP资源
    
    alt 找到CEP
        Agent->>K8s: 删除CEP
        Note right of K8s: kubectl delete cep <name>
        K8s-->>Agent: 删除成功
        Agent->>CNI: 返回成功
    else 未找到CEP
        Agent->>CNI: 返回成功 (幂等)
    end
```

## 4. Operator实现

### 4.1 独占模式ADD流程

```mermaid
sequenceDiagram
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over CEP,Cloud: 【独占模式ADD流程】
    
    CEP->>Operator: CEP创建事件
    Note right of CEP: type: CrossVPCPrimary
    
    Operator->>Operator: 可重入检查
    Note right of Operator: 检查是否已有ENI CR
    
    alt 无现有ENI CR
        Operator->>ENIC: 创建ENI CR
        Note right of ENIC: spec:<br/>- useMode: CrossVPCPrimary<br/>- instanceID: node-123<br/>- crossVPCConfig<br/>- deletionPolicy: immediate
        
        ENISM->>ENIC: 监听ENI CR创建
        ENISM->>Cloud: 创建ENI (不指定instanceID)
        Cloud-->>ENISM: 返回ENI ID
        ENISM->>Cloud: 挂载ENI到实例
        Cloud-->>ENISM: 挂载完成
        ENISM->>ENIC: 更新ENI状态 (包含MAC地址)
        
        Operator->>CEP: 更新CEP状态
        Note right of CEP: status:<br/>- ip: *************<br/>- eniID: eni-abc123<br/>- macAddress: 02:00:00:12:34:56<br/>- state: Ready
        
    else 有现有ENI CR
        Operator->>ENIC: 查询ENI CR状态
        Operator->>CEP: 更新CEP状态
    end
```

### 4.2 共享模式ADD流程

```mermaid
sequenceDiagram
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant Cloud as Cloud API

    Note over CEP,Cloud: 【共享模式ADD流程 - 参考非reuse IP的PSTS】
    
    CEP->>Operator: CEP创建事件
    Note right of CEP: type: CrossVPCSecondary
    
    Operator->>Operator: 构造DirectIPAction
    Note right of Operator: action:<br/>- subnetID<br/>- crossVPCConfig<br/>- mode: secondary
    
    Operator->>Cloud: 直接分配子网IP
    Note right of Cloud: AllocatePrivateIP(<br/>  subnetID, userID<br/>)
    
    alt 分配成功
        Cloud-->>Operator: 返回IP信息
        Operator->>CEP: 更新CEP状态
        Note right of CEP: status:<br/>- ip: *************<br/>- gateway: ***********<br/>- state: Ready
        
    else 分配失败
        Cloud-->>Operator: 返回错误
        Operator->>CEP: 更新错误状态
        Note right of CEP: status:<br/>- state: Failed<br/>- errorCode: QUOTA_EXCEEDED
    end
```

### 4.3 删除流程

```mermaid
sequenceDiagram
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over CEP,Cloud: 【删除流程】
    
    CEP->>Operator: CEP删除事件
    
    alt 独占模式
        Operator->>ENIC: 标记ENI CR删除
        Note right of ENIC: 添加deletionTimestamp
        
        ENISM->>ENIC: 监听删除事件
        ENISM->>Cloud: 分离ENI
        ENISM->>Cloud: 删除ENI
        ENISM->>ENIC: 移除finalizer
        
    else 共享模式
        Operator->>Cloud: 释放IP
        Note right of Cloud: ReleasePrivateIP(ip)
        Cloud-->>Operator: 释放成功
    end
    
    Operator->>CEP: 清理CEP资源
```

## 5. ENI状态机GC流程

```mermaid
sequenceDiagram
    participant Timer as GC定时器
    participant ENISM as ENI State Machine
    participant ENIC as ENI CR
    participant CEP as CEP Store
    participant Cloud as Cloud API

    Note over Timer,Cloud: 【ENI状态机GC流程】
    
    Timer->>ENISM: 触发GC (每2分钟)
    ENISM->>ENIC: 扫描共享模式ENI CR
    
    loop 遍历每个ENI CR
        ENISM->>ENIC: 检查secondaryIPCount
        
        alt 有辅助IP
            ENISM->>ENISM: 跳过删除
        else 无辅助IP
            ENISM->>CEP: 检查CEP使用情况
            
            alt 有CEP使用
                ENISM->>ENISM: 跳过删除
            else 无CEP使用
                ENISM->>ENISM: 检查过期时间
                
                alt 超过延迟时间
                    ENISM->>Cloud: 查询ENI真实状态
                    
                    alt 确认无辅助IP
                        ENISM->>Cloud: 删除ENI
                        ENISM->>ENIC: 移除finalizer
                    else 有辅助IP
                        ENISM->>ENISM: 重新开始计时
                    end
                else 未超过延迟时间
                    ENISM->>ENISM: 继续等待
                end
            end
        end
    end
```

## 6. 关键流程图

### 6.1 CNI插件决策流程

```mermaid
flowchart TD
    A[CNI调用] --> B{解析配置}
    B --> C{模式判断}
    
    C -->|独占模式| D[查找ENI设备]
    C -->|共享模式| E[创建veth pair]
    
    D --> F{找到ENI设备?}
    F -->|是| G[移动到容器netns]
    F -->|否| H[返回错误]
    
    E --> I[配置辅助IP]
    
    G --> J[配置IP和路由]
    I --> J
    J --> K[返回成功]
    
    style C fill:#e3f2fd
    style D fill:#fff3e0
    style E fill:#e8f5e8
```

### 6.2 Operator模式选择流程

```mermaid
flowchart TD
    A[CEP事件] --> B{检查类型}
    
    B -->|CrossVPCPrimary| C[独占模式处理]
    B -->|CrossVPCSecondary| D[共享模式处理]
    
    C --> E[创建ENI CR]
    E --> F[ENI状态机处理]
    F --> G[更新CEP状态]
    
    D --> H[构造DirectIPAction]
    H --> I[直接分配IP]
    I --> J[更新CEP状态]
    
    G --> K[完成]
    J --> K
    
    style B fill:#e3f2fd
    style C fill:#fff3e0
    style D fill:#e8f5e8
```

## 7. 配置示例

### 7.1 CNI配置

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-network",
  "type": "crossvpc-primary",  // 或 "crossvpc-secondary"
  "crossvpc": {
    "userID": "user123",
    "subnetID": "sbn-abc123",
    "securityGroups": ["sg-123456"],
    "vpcCIDR": "***********/16"
  },
  "ipam": {
    "type": "cce-network-agent"
  }
}
```

### 7.2 Operator配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  crossvpc-eni-enabled: "true"
  crossvpc-eni-shared-deletion-delay: "300s"
  crossvpc-eni-gc-interval: "120s"
```

这个完整方案通过CNI插件、Agent和Operator的协同工作，实现了CrossVPC ENI的完整生命周期管理，支持独占和共享两种模式，具有良好的扩展性和可维护性。
