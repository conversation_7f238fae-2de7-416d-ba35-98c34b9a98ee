# CrossVPCEni ENI状态机管理方案

## 1. ENI状态机设计

### 1.1 核心理念
- **分离关注点**：Operator负责创建ENI CR，ENI状态机负责实际的云平台操作
- **状态驱动**：通过ENI CR的状态变化驱动挂载和删除操作
- **删除标记**：使用deletionTimestamp标记删除，支持优雅删除
- **可配置延迟**：共享模式支持可配置的删除延迟时间

### 1.2 ENI CR扩展

```go
// pkg/k8s/apis/cce.baidubce.com/v2/cce_eni_types.go
type ENISpec struct {
    models.ENI `json:",inline"`
    
    // 现有字段
    NodeName                  string     `json:"nodeName"`
    UseMode                   ENIUseMode `json:"useMode"`
    
    // 新增字段
    InstanceID                string                    `json:"instanceID"`           // 目标实例ID
    CrossVPCConfig           *CrossVPCConfig           `json:"crossVPCConfig,omitempty"`
    DeletionPolicy           *ENIDeletionPolicy        `json:"deletionPolicy,omitempty"`
}

type CrossVPCConfig struct {
    UserID                           string   `json:"userID"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

type ENIDeletionPolicy struct {
    Mode                string `json:"mode"`                // "immediate" | "delayed"
    DelaySeconds        int    `json:"delaySeconds"`        // 延迟删除秒数
    LastIPReleasedTime  string `json:"lastIPReleasedTime"`  // 最后一个IP释放时间
}

type ENIStatus struct {
    // 现有字段
    ENI                models.ENI                   `json:"eni,omitempty"`
    VPCStatus          VPCStatus                    `json:"vpcStatus,omitempty"`
    
    // 新增字段
    AttachmentStatus   ENIAttachmentStatus          `json:"attachmentStatus,omitempty"`
    SecondaryIPCount   int                          `json:"secondaryIPCount"`
    DeletionScheduled  *metav1.Time                 `json:"deletionScheduled,omitempty"`
}

type ENIAttachmentStatus struct {
    Phase       string `json:"phase"`       // "Pending", "Attaching", "Attached", "Detaching", "Detached"
    Message     string `json:"message"`
    LastUpdated string `json:"lastUpdated"`
}
```

## 2. ENI状态机流程

### 2.1 创建和挂载流程

```mermaid
sequenceDiagram
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API
    participant CEP as CCEEndpoint

    Note over Operator,CEP: 【ENI创建阶段】
    Operator->>ENIC: 创建ENI CR
    Note right of ENIC: spec:<br/>- instanceID: node-123<br/>- subnetID: sbn-abc<br/>- useMode: CrossVPCPrimary
    
    Note over ENISM,Cloud: 【ENI状态机处理】
    ENISM->>ENIC: 监听ENI CR创建
    ENISM->>Cloud: 创建ENI (不指定instanceID)
    Cloud-->>ENISM: 返回ENI ID
    ENISM->>ENIC: 更新status.eni.id
    
    ENISM->>Cloud: 挂载ENI到实例
    Note right of Cloud: AttachENI(eniID, instanceID)
    Cloud-->>ENISM: 挂载中
    ENISM->>ENIC: 更新attachmentStatus=Attaching
    
    loop 等待挂载完成
        ENISM->>Cloud: 查询ENI状态
        Cloud-->>ENISM: 返回状态
        alt 挂载完成
            ENISM->>ENIC: 更新attachmentStatus=Attached
            ENISM->>ENIC: 更新status.eni (包含MAC地址)
        else 挂载中
            ENISM->>ENISM: 等待1秒后重试
        end
    end
    
    Note over Operator,CEP: 【Operator更新CEP】
    Operator->>ENIC: 监听ENI状态变化
    Operator->>CEP: 更新ENI信息到CEP
```

### 2.2 删除流程

```mermaid
sequenceDiagram
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over Operator,Cloud: 【删除标记阶段】
    Operator->>ENIC: 标记删除 (deletionTimestamp)
    
    alt 独占模式
        Note over ENISM: 立即删除
        ENISM->>ENIC: 检测到deletionTimestamp
        ENISM->>Cloud: 分离ENI
        ENISM->>Cloud: 删除ENI
        ENISM->>ENIC: 移除finalizer
    else 共享模式
        Note over ENISM: 延迟删除
        ENISM->>ENIC: 检测到deletionTimestamp
        ENISM->>ENIC: 检查是否还有辅助IP
        
        alt 还有辅助IP
            ENISM->>ENIC: 等待IP释放
        else 无辅助IP
            ENISM->>ENIC: 计算删除时间
            Note right of ENIC: deletionScheduled = <br/>lastIPReleasedTime + delaySeconds
            
            alt 到达删除时间
                ENISM->>Cloud: 分离ENI
                ENISM->>Cloud: 删除ENI
                ENISM->>ENIC: 移除finalizer
            else 未到删除时间
                ENISM->>ENISM: 等待到删除时间
            end
        end
    end
```

## 3. 详细实现

### 3.1 Operator创建ENI CR

```go
// CrossVPC Operator创建ENI CR
func (c *CrossVPCController) createENICR(ctx context.Context, cep *ccev2.CCEEndpoint) (*ccev2.ENI, error) {
    config := cep.Spec.Network.IPAllocation.CrossVPCConfig
    
    eniName := generateENIName(cep.Namespace, cep.Name, config.Mode)
    
    eni := &ccev2.ENI{
        ObjectMeta: metav1.ObjectMeta{
            Name: eniName,
            Labels: map[string]string{
                "cce.baidubce.com/eni-type":     "crossvpc",
                "cce.baidubce.com/eni-mode":     config.Mode,
                "cce.baidubce.com/owner-pod":    cep.Name,
                "cce.baidubce.com/owner-ns":     cep.Namespace,
            },
            Finalizers: []string{"eni.cce.baidubce.com/cleanup"},
        },
        Spec: ccev2.ENISpec{
            NodeName:   cep.Spec.Network.IPAllocation.NodeName,
            InstanceID: c.getInstanceID(cep.Spec.Network.IPAllocation.NodeName),
            UseMode:    getENIUseMode(config.Mode),
            ENI: models.ENI{
                SubnetID:         config.SubnetID,
                SecurityGroupIds: config.SecurityGroupIDs,
            },
            CrossVPCConfig: &ccev2.CrossVPCConfig{
                UserID:  config.UserID,
                VPCCIDR: config.VPCCIDR,
            },
            DeletionPolicy: &ccev2.ENIDeletionPolicy{
                Mode:         getDeletionMode(config.Mode),
                DelaySeconds: getDeletionDelay(config.Mode),
            },
        },
    }
    
    return c.eniClient.ENIs().Create(ctx, eni, metav1.CreateOptions{})
}

func getDeletionMode(mode string) string {
    if mode == "primary" {
        return "immediate"
    }
    return "delayed"
}

func getDeletionDelay(mode string) int {
    if mode == "primary" {
        return 0
    }
    // 共享模式默认延迟5分钟，可通过配置调整
    return operatorOption.Config.CrossVPCENISharedDeletionDelay
}
```

### 3.2 ENI状态机实现

```go
// ENI状态机处理
func (esm *eniStateMachine) handleCrossVPCENI() error {
    eni := esm.resource
    
    // 检查是否标记删除
    if eni.DeletionTimestamp != nil {
        return esm.handleDeletion()
    }
    
    // 检查ENI是否已创建
    if eni.Status.ENI.ID == "" {
        return esm.createENI()
    }
    
    // 检查ENI是否已挂载
    if eni.Status.AttachmentStatus.Phase != "Attached" {
        return esm.handleAttachment()
    }
    
    // ENI已就绪，处理辅助IP管理
    return esm.handleSecondaryIPs()
}

func (esm *eniStateMachine) createENI() error {
    spec := esm.resource.Spec
    
    // 创建ENI（不指定instanceID）
    createArgs := &enisdk.CreateEniArgs{
        Name:             esm.resource.Name,
        SubnetId:         spec.ENI.SubnetID,
        SecurityGroupIds: spec.ENI.SecurityGroupIds,
        Description:      fmt.Sprintf("CrossVPC ENI for %s", spec.UseMode),
        Tags: []enisdk.TagModel{
            {TagKey: "CrossVPC", TagValue: "true"},
            {TagKey: "Mode", TagValue: string(spec.UseMode)},
            {TagKey: "UserID", TagValue: spec.CrossVPCConfig.UserID},
        },
    }
    
    eniID, err := esm.bceClient.CreateENI(context.Background(), createArgs)
    if err != nil {
        return fmt.Errorf("failed to create ENI: %w", err)
    }
    
    // 更新ENI状态
    esm.resource.Status.ENI.ID = eniID
    esm.resource.Status.AttachmentStatus = ccev2.ENIAttachmentStatus{
        Phase:       "Pending",
        Message:     "ENI created, waiting for attachment",
        LastUpdated: time.Now().Format(time.RFC3339),
    }
    
    return nil
}

func (esm *eniStateMachine) handleAttachment() error {
    eniID := esm.resource.Status.ENI.ID
    instanceID := esm.resource.Spec.InstanceID
    
    switch esm.resource.Status.AttachmentStatus.Phase {
    case "Pending":
        // 开始挂载
        err := esm.bceClient.AttachENI(context.Background(), &enisdk.AttachEniArgs{
            EniId:      eniID,
            InstanceId: instanceID,
        })
        if err != nil {
            return fmt.Errorf("failed to attach ENI: %w", err)
        }
        
        esm.resource.Status.AttachmentStatus.Phase = "Attaching"
        esm.resource.Status.AttachmentStatus.Message = "ENI attachment in progress"
        esm.resource.Status.AttachmentStatus.LastUpdated = time.Now().Format(time.RFC3339)
        
    case "Attaching":
        // 检查挂载状态
        eni, err := esm.bceClient.GetENI(context.Background(), eniID)
        if err != nil {
            return fmt.Errorf("failed to get ENI status: %w", err)
        }
        
        if eni.InstanceID == instanceID && eni.Status == "inuse" {
            // 挂载完成
            esm.resource.Status.ENI = *eni
            esm.resource.Status.AttachmentStatus.Phase = "Attached"
            esm.resource.Status.AttachmentStatus.Message = "ENI attached successfully"
            esm.resource.Status.AttachmentStatus.LastUpdated = time.Now().Format(time.RFC3339)
        }
    }
    
    return nil
}
```

### 3.3 删除处理

```go
func (esm *eniStateMachine) handleDeletion() error {
    eni := esm.resource
    deletionPolicy := eni.Spec.DeletionPolicy
    
    if deletionPolicy.Mode == "immediate" {
        // 独占模式：立即删除
        return esm.performDeletion()
    }
    
    // 共享模式：延迟删除
    return esm.handleDelayedDeletion()
}

func (esm *eniStateMachine) handleDelayedDeletion() error {
    eni := esm.resource
    
    // 检查是否还有辅助IP
    if eni.Status.SecondaryIPCount > 0 {
        // 还有IP在使用，等待释放
        return nil
    }
    
    // 计算删除时间
    if eni.Status.DeletionScheduled == nil {
        deletionTime := time.Now().Add(time.Duration(eni.Spec.DeletionPolicy.DelaySeconds) * time.Second)
        eni.Status.DeletionScheduled = &metav1.Time{Time: deletionTime}
        return nil
    }
    
    // 检查是否到达删除时间
    if time.Now().Before(eni.Status.DeletionScheduled.Time) {
        // 未到删除时间，等待
        return nil
    }
    
    // 到达删除时间，执行删除
    return esm.performDeletion()
}

func (esm *eniStateMachine) performDeletion() error {
    eniID := esm.resource.Status.ENI.ID
    instanceID := esm.resource.Spec.InstanceID
    
    // 1. 分离ENI
    if esm.resource.Status.AttachmentStatus.Phase == "Attached" {
        err := esm.bceClient.DetachENI(context.Background(), &enisdk.DetachEniArgs{
            EniId:      eniID,
            InstanceId: instanceID,
        })
        if err != nil {
            return fmt.Errorf("failed to detach ENI: %w", err)
        }
        
        esm.resource.Status.AttachmentStatus.Phase = "Detaching"
        return nil // 等待下次reconcile检查分离状态
    }
    
    // 2. 检查分离状态
    if esm.resource.Status.AttachmentStatus.Phase == "Detaching" {
        eni, err := esm.bceClient.GetENI(context.Background(), eniID)
        if err != nil {
            return fmt.Errorf("failed to get ENI status: %w", err)
        }
        
        if eni.InstanceID == "" && eni.Status == "available" {
            esm.resource.Status.AttachmentStatus.Phase = "Detached"
        } else {
            return nil // 继续等待分离完成
        }
    }
    
    // 3. 删除ENI
    if esm.resource.Status.AttachmentStatus.Phase == "Detached" {
        err := esm.bceClient.DeleteENI(context.Background(), eniID)
        if err != nil {
            return fmt.Errorf("failed to delete ENI: %w", err)
        }
        
        // 移除finalizer，允许删除ENI CR
        esm.resource.Finalizers = removeFinalizer(esm.resource.Finalizers, "eni.cce.baidubce.com/cleanup")
    }
    
    return nil
}
```

### 3.4 配置管理

```go
// 配置文件扩展
type CrossVPCENIConfig struct {
    Enabled                    bool          `json:"enabled"`
    SharedDeletionDelay        int           `json:"sharedDeletionDelay"`        // 共享模式删除延迟（秒）
    PrimaryDeletionDelay       int           `json:"primaryDeletionDelay"`       // 独占模式删除延迟（秒）
    AttachmentTimeout          time.Duration `json:"attachmentTimeout"`          // 挂载超时时间
    DetachmentTimeout          time.Duration `json:"detachmentTimeout"`          // 分离超时时间
}

// 默认配置
var DefaultCrossVPCENIConfig = CrossVPCENIConfig{
    Enabled:              false,
    SharedDeletionDelay:  300,  // 5分钟
    PrimaryDeletionDelay: 0,    // 立即删除
    AttachmentTimeout:    5 * time.Minute,
    DetachmentTimeout:    2 * time.Minute,
}
```

## 4. 配置示例

### 4.1 ConfigMap配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  # CrossVPC ENI配置
  enable-crossvpc-eni: "true"
  crossvpc-eni-shared-deletion-delay: "300"    # 共享模式删除延迟5分钟
  crossvpc-eni-primary-deletion-delay: "0"     # 独占模式立即删除
  crossvpc-eni-attachment-timeout: "300s"      # 挂载超时5分钟
  crossvpc-eni-detachment-timeout: "120s"      # 分离超时2分钟
```

### 4.2 ENI CR示例

```yaml
# 独占模式ENI CR
apiVersion: cce.baidubce.com/v2
kind: ENI
metadata:
  name: crossvpc-primary-pod-abc123
  finalizers:
  - eni.cce.baidubce.com/cleanup
spec:
  nodeName: "worker-node-1"
  instanceID: "i-abc123def456"
  useMode: "CrossVPCPrimary"
  subnetID: "sbn-abc123"
  securityGroupIds: ["sg-123456"]
  crossVPCConfig:
    userID: "user123"
    vpcCIDR: "***********/16"
  deletionPolicy:
    mode: "immediate"
    delaySeconds: 0
status:
  eni:
    id: "eni-abc123def456"
    macAddress: "02:00:00:12:34:56"
    primaryIP: "*************"
  attachmentStatus:
    phase: "Attached"
    message: "ENI attached successfully"
    lastUpdated: "2024-01-01T12:00:00Z"

---
# 共享模式ENI CR
apiVersion: cce.baidubce.com/v2
kind: ENI
metadata:
  name: crossvpc-shared-user123-def456
  finalizers:
  - eni.cce.baidubce.com/cleanup
spec:
  nodeName: "worker-node-1"
  instanceID: "i-abc123def456"
  useMode: "CrossVPCSecondary"
  subnetID: "sbn-abc123"
  securityGroupIds: ["sg-123456"]
  crossVPCConfig:
    userID: "user123"
    vpcCIDR: "***********/16"
  deletionPolicy:
    mode: "delayed"
    delaySeconds: 300
status:
  eni:
    id: "eni-def456abc123"
    macAddress: "02:00:00:65:43:21"
    primaryIP: "*************"
  attachmentStatus:
    phase: "Attached"
    message: "ENI attached successfully"
    lastUpdated: "2024-01-01T12:00:00Z"
  secondaryIPCount: 2
  deletionScheduled: "2024-01-01T12:05:00Z"  # 当secondaryIPCount=0时设置
```

## 5. 优势总结

### 5.1 分离关注点
- **Operator专注业务逻辑**：只负责创建ENI CR和更新CEP
- **ENI状态机专注资源管理**：负责实际的云平台操作
- **清晰的责任边界**：避免逻辑混乱和重复代码

### 5.2 灵活的删除策略
- **独占模式立即删除**：Pod删除后立即清理ENI
- **共享模式延迟删除**：可配置延迟时间，避免频繁创建删除
- **优雅删除**：通过deletionTimestamp和finalizer确保资源正确清理

### 5.3 状态可观测
- **详细的挂载状态**：Pending → Attaching → Attached
- **删除调度信息**：可以看到预计删除时间
- **错误信息记录**：便于故障排查和运维

这个ENI状态机方案通过分离关注点和状态驱动的设计，实现了更加清晰和可维护的ENI生命周期管理。
