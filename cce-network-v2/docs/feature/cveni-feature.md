# CrossVPCEni (CVENI) 实现文档

## 概述

CrossVPCEni（跨VPC弹性网络接口）是百度云CCE CNI驱动中用于实现跨VPC网络连接的核心功能。它允许Pod使用来自不同VPC的ENI，实现跨VPC的网络通信，为容器提供灵活的网络架构。

## 1. CrossVPCEni 字段详解

### 1.1 核心结构

```go
type CrossVPCEni struct {
    metav1.TypeMeta   `json:",inline"`
    metav1.ObjectMeta `json:"metadata,omitempty"`
    Spec   CrossVPCEniSpec   `json:"spec,omitempty"`
    Status CrossVPCEniStatus `json:"status,omitempty"`
}
```

### 1.2 Spec 字段说明

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `UserID` | string | 是 | 用户ID，标识此ENI所属的用户 |
| `SubnetID` | string | 是 | 子网ID，指定创建ENI的子网 |
| `SecurityGroupIDs` | []string | 是 | 安全组ID列表，绑定到ENI的安全组 |
| `VPCCIDR` | string | 是 | VPC的CIDR地址段 |
| `PrivateIPAddress` | string | 否 | 创建ENI时使用的私有IP地址 |
| `BoundInstanceID` | string | 是 | 绑定实例ID，ENI将附加到的节点实例ID |
| `DefaultRouteInterfaceDelegation` | string | 否 | 默认路由接口委托类型，目前仅支持"eni" |
| `DefaultRouteExcludedCidrs` | []string | 否 | 从默认路由中排除的CIDR列表 |

### 1.3 Status 字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `EniID` | string | ENI的唯一标识符 |
| `EniStatus` | EniStatus | ENI的当前状态 |
| `PrimaryIPAddress` | string | ENI的主IP地址 |
| `MacAddress` | string | ENI的MAC地址 |
| `VPCID` | string | ENI所属的VPC ID |
| `InvolvedContainerID` | string | 关联的容器ID |
| `Conditions` | []CrossVPCEniCondition | ENI的状态条件列表 |

### 1.4 EniStatus 状态枚举

```go
const (
    EniStatusPending   = "pending"    // 等待创建
    EniStatusCreated   = "created"    // 已创建
    EniStatusAttaching = "attaching"  // 正在附加
    EniStatusInuse     = "inuse"      // 使用中
    EniStatusDetaching = "detaching"  // 正在分离
    EniStatusDetached  = "detached"   // 已分离
    EniStatusDeleted   = "deleted"    // 已删除
)
```

### 1.5 Pod 注解字段

| 注解名 | 描述 |
|--------|------|
| `cross-vpc-eni.cce.io/userID` | 用户ID |
| `cross-vpc-eni.cce.io/subnetID` | 子网ID |
| `cross-vpc-eni.cce.io/securityGroupIDs` | 安全组ID列表（逗号分隔） |
| `cross-vpc-eni.cce.io/privateIPAddress` | 私有IP地址 |
| `cross-vpc-eni.cce.io/vpcCidr` | VPC CIDR |
| `cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation` | 默认路由接口委托 |
| `cross-vpc-eni.cce.io/defaultRouteExcludedCidrs` | 排除的CIDR列表（逗号分隔） |

## 2. CrossVPCEni 生命周期

### 2.1 生命周期状态转换

```mermaid
stateDiagram-v2
    [*] --> Pending: 创建CRD
    Pending --> Created: 云平台创建ENI
    Created --> Attaching: 开始附加到实例
    Attaching --> Inuse: 附加完成
    Inuse --> Detaching: 开始分离
    Detaching --> Detached: 分离完成
    Detached --> Deleted: 删除ENI
    Deleted --> [*]
    
    Pending --> Deleted: 创建失败
    Created --> Deleted: 创建后直接删除
    Attaching --> Deleted: 附加失败
    Detaching --> Deleted: 分离失败
```

### 2.2 生命周期管理

#### 2.2.1 创建阶段
1. **Pending**: IPAM创建CrossVPCEni CRD资源
2. **Created**: 云平台控制器创建ENI实例
3. **Attaching**: ENI开始附加到目标实例
4. **Inuse**: ENI成功附加并可供Pod使用

#### 2.2.2 释放阶段
1. **Detaching**: 开始从实例分离ENI
2. **Detached**: ENI成功分离
3. **Deleted**: 删除ENI实例和CRD资源

## 3. 核心流程

### 3.1 Pod创建时的ENI分配流程

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant CNI as CNI Plugin
    participant IPAM as IPAM Service
    participant CRD as CrossVPCEni CRD
    participant Cloud as Cloud Controller
    participant Node as Node

    Pod->>CNI: 创建Pod，触发CNI ADD
    CNI->>IPAM: AllocateIP请求
    IPAM->>IPAM: 检查Pod注解
    IPAM->>IPAM: 验证节点状态
    IPAM->>CRD: 创建CrossVPCEni资源
    CRD-->>IPAM: 返回CRD对象
    IPAM->>IPAM: 设置状态为Pending
    IPAM-->>CNI: 返回ENI信息
    
    Note over Cloud,Node: 异步处理
    Cloud->>Cloud: 创建ENI实例
    Cloud->>Node: 附加ENI到实例
    Cloud->>CRD: 更新状态为Inuse
    
    CNI->>CNI: 等待ENI状态变为Inuse
    CNI->>Node: 配置网络接口
    CNI->>Pod: 返回网络配置
```

### 3.2 Pod删除时的ENI释放流程

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant CNI as CNI Plugin
    participant IPAM as IPAM Service
    participant CRD as CrossVPCEni CRD
    participant Cloud as Cloud Controller
    participant Node as Node

    Pod->>CNI: 删除Pod，触发CNI DEL
    CNI->>IPAM: ReleaseIP请求
    IPAM->>CRD: 查找CrossVPCEni资源
    IPAM->>CRD: 删除CrossVPCEni资源
    IPAM-->>CNI: 返回释放结果
    
    Note over Cloud,Node: 异步处理
    Cloud->>Node: 分离ENI
    Cloud->>Cloud: 删除ENI实例
    Cloud->>CRD: 更新状态为Deleted
    
    CNI->>Node: 清理网络配置
    CNI->>Pod: 完成网络清理
```

### 3.3 IPAM分配流程

```mermaid
flowchart TD
    A[接收Allocate请求] --> B{检查IPAM就绪状态}
    B -->|未就绪| C[返回错误]
    B -->|已就绪| D[获取Pod信息]
    D --> E[获取Node信息]
    E --> F{检查Pod注解}
    F -->|无注解| G[返回nil，跳过处理]
    F -->|有注解| H{检查节点ENI状态}
    H -->|不稳定| I[返回错误]
    H -->|稳定| J[从注解提取ENI配置]
    J --> K[创建CrossVPCEni CRD]
    K --> L{创建成功?}
    L -->|失败| M[返回错误]
    L -->|成功| N[设置状态为Pending]
    N --> O[等待状态变为Inuse]
    O --> P{超时?}
    P -->|是| Q[返回超时错误]
    P -->|否| R[返回ENI信息]
```

### 3.4 IPAM释放流程

```mermaid
flowchart TD
    A[接收Release请求] --> B{检查IPAM就绪状态}
    B -->|未就绪| C[返回错误]
    B -->|已就绪| D[查找CrossVPCEni资源]
    D --> E{资源存在?}
    E -->|不存在| F[返回nil]
    E -->|存在| G{检查节点ENI状态}
    G -->|不稳定| H[返回错误]
    G -->|稳定| I[删除CrossVPCEni CRD]
    I --> J{删除成功?}
    J -->|失败| K[返回错误]
    J -->|成功| L[等待状态变为Deleted]
    L --> M{超时?}
    M -->|是| N[返回超时错误]
    M -->|否| O[返回成功]
```

### 3.5 CNI插件网络配置流程

```mermaid
flowchart TD
    A[CNI ADD调用] --> B[解析配置]
    B --> C[调用IPAM分配ENI]
    C --> D{分配成功?}
    D -->|失败| E[返回错误]
    D -->|成功| F[等待ENI就绪]
    F --> G[获取网络命名空间]
    G --> H[查找ENI网络接口]
    H --> I[移动接口到容器命名空间]
    I --> J[配置IP地址]
    J --> K[配置路由]
    K --> L[设置默认路由]
    L --> M[配置用户指定路由]
    M --> N[禁用IP转发]
    N --> O[返回网络配置]
```

## 4. 关键组件详解

### 4.1 IPAM组件 (`pkg/eniipam/ipam/crossvpceni/ipam.go`)

#### 4.1.1 核心方法
- **Allocate**: 分配CrossVPCEni资源
- **Release**: 释放CrossVPCEni资源
- **Ready**: 检查IPAM就绪状态
- **Run**: 启动IPAM控制器
- **gc**: 垃圾回收机制

#### 4.1.2 关键特性
- **并发控制**: 支持QPS限制和并发控制
- **状态检查**: 检查节点ENI状态，避免冲突
- **超时重试**: 使用指数退避算法等待状态变化
- **垃圾回收**: 定期清理泄漏的CrossVPCEni资源

### 4.2 CNI插件 (`cni/crossvpc-eni/`)

#### 4.2.1 核心功能
- **网络配置**: 配置Pod的网络接口和路由
- **接口管理**: 管理ENI接口的移动和配置
- **路由设置**: 设置默认路由和用户指定路由
- **安全配置**: 禁用IP转发等安全设置

#### 4.2.2 支持模式
- **主接口模式**: ENI作为Pod的主要网络接口
- **辅助接口模式**: ENI作为Pod的辅助网络接口

### 4.3 GRPC服务 (`pkg/eniipam/grpc/handler.go`)

#### 4.3.1 服务接口
- **AllocateIP**: 处理IP分配请求
- **ReleaseIP**: 处理IP释放请求
- **CheckIP**: 处理IP检查请求

#### 4.3.2 监控指标
- **RPC延迟**: 记录RPC调用延迟
- **错误计数**: 统计RPC错误次数
- **并发控制**: 监控并发请求数量

## 5. 配置示例

### 5.1 Pod注解配置

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: crossvpc-pod
  annotations:
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123,sg-456"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    cross-vpc-eni.cce.io/privateIPAddress: "*************"
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "eni"
    cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "10.0.0.0/8,**********/12"
spec:
  containers:
  - name: app
    image: nginx
```

### 5.2 CNI配置

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-eni",
  "type": "crossvpc-eni",
  "ifName": "eth0",
  "endpoint": "unix:///var/run/cce-ipam.sock"
}
```

## 6. 监控和故障排查

### 6.1 关键日志

#### 6.1.1 IPAM日志
```
[Allocate] allocating eni for pod (default/my-pod) starts
create crossvpceni abc123 for pod (default/my-pod)
crossvpceni abc123 current status: inuse
[Allocate] allocating eni for pod (default/my-pod) ends
```

#### 6.1.2 CNI日志
```
====> CmdAdd Begins <====
ipam creates eni error: timeout waiting for ENI
====> CmdAdd Ends <====
```

### 6.2 监控指标

- **ENI分配成功率**: 监控ENI分配的成功率
- **ENI分配延迟**: 监控ENI分配的耗时
- **节点ENI数量**: 监控每个节点的ENI使用情况
- **错误率**: 监控各种错误的发生率

### 6.3 常见问题

#### 6.3.1 ENI分配失败
- **原因**: 节点ENI数量超限、云平台配额不足
- **排查**: 检查节点ENI数量、云平台配额

#### 6.3.2 ENI附加超时
- **原因**: 云平台响应慢、网络问题
- **排查**: 检查云平台状态、网络连通性

#### 6.3.3 路由配置错误
- **原因**: CIDR配置错误、路由冲突
- **排查**: 检查CIDR配置、路由表

## 7. 最佳实践

### 7.1 性能优化
- 合理设置ENI数量限制
- 使用预分配机制减少分配延迟
- 配置合适的超时时间

### 7.2 安全考虑
- 严格控制安全组配置
- 使用私有IP地址
- 禁用不必要的网络功能

### 7.3 运维建议
- 定期监控ENI使用情况
- 及时清理泄漏的ENI资源
- 建立完善的告警机制

## 8. 总结

CrossVPCEni实现了一个完整的跨VPC网络解决方案，通过以下核心特性提供灵活的网络架构：

1. **灵活的配置**: 支持多种网络配置选项
2. **可靠的状态管理**: 完善的生命周期状态管理
3. **高效的资源管理**: 智能的垃圾回收和并发控制
4. **完善的监控**: 全面的监控和故障排查能力

该实现为容器云平台提供了强大的跨VPC网络能力，支持复杂的网络架构需求。 