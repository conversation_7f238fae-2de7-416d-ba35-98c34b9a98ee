# CrossVPCEni 关键代码示例

## 1. CRD定义示例

### 1.1 CrossVPCEni类型定义

```go
// pkg/k8s/apis/cce.baidubce.com/v2/crossvpc_eni_types.go
package v2

import (
    metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// CrossVPCEni 跨VPC ENI资源定义
// +genclient
// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type CrossVPCEni struct {
    metav1.TypeMeta   `json:",inline"`
    metav1.ObjectMeta `json:"metadata,omitempty"`
    Spec   CrossVPCEniSpec   `json:"spec,omitempty"`
    Status CrossVPCEniStatus `json:"status,omitempty"`
}

type CrossVPCEniSpec struct {
    UserID                           string   `json:"userID"`
    SubnetID                         string   `json:"subnetID"`
    SecurityGroupIDs                 []string `json:"securityGroupIDs"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    PrivateIPAddress                 string   `json:"privateIPAddress,omitempty"`
    BoundInstanceID                  string   `json:"boundInstanceID"`
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

type CrossVPCEniStatus struct {
    EniID               string                    `json:"eniID,omitempty"`
    EniStatus           EniStatus                 `json:"eniStatus"`
    PrimaryIPAddress    string                    `json:"primaryIPAddress,omitempty"`
    MacAddress          string                    `json:"macAddress,omitempty"`
    VPCID               string                    `json:"vpcID,omitempty"`
    InvolvedContainerID string                    `json:"involvedContainerID,omitempty"`
    Conditions          []CrossVPCEniCondition    `json:"conditions,omitempty"`
}
```

## 2. IPAM分配器示例

### 2.1 CrossVPCEni分配器核心逻辑

```go
// pkg/ipam/crossvpc_eni_allocator.go
package ipam

import (
    "context"
    "fmt"
    "net"
    "strings"
    "time"
    
    "github.com/sirupsen/logrus"
    metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
    
    ccev2 "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s/apis/cce.baidubce.com/v2"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s"
)

type crossVPCEniAllocator struct {
    client k8s.CCEClient
    family Family
}

func newCrossVPCEniAllocator(family Family, c Configuration, owner string, k8sEventReg K8sEventRegister) Allocator {
    return &crossVPCEniAllocator{
        client: k8s.CCEClient(),
        family: family,
    }
}

func (c *crossVPCEniAllocator) Allocate(ctx context.Context, owner string) (*AllocationResult, error) {
    // 1. 解析Pod信息
    podNamespace, podName, err := parsePodOwner(owner)
    if err != nil {
        return nil, fmt.Errorf("failed to parse pod owner: %w", err)
    }
    
    // 2. 获取Pod注解
    pod, err := k8s.CCEClient().CoreV1().Pods(podNamespace).Get(ctx, podName, metav1.GetOptions{})
    if err != nil {
        return nil, fmt.Errorf("failed to get pod: %w", err)
    }
    
    // 3. 检查是否有CrossVPCEni注解
    if !hasCrossVPCEniAnnotations(pod.Annotations) {
        return nil, fmt.Errorf("pod does not have CrossVPCEni annotations")
    }
    
    // 4. 解析注解构造Spec
    spec, err := c.parseCrossVPCEniAnnotations(pod.Annotations)
    if err != nil {
        return nil, fmt.Errorf("failed to parse annotations: %w", err)
    }
    
    // 5. 创建CrossVPCEni资源
    crossVPCEni := &ccev2.CrossVPCEni{
        ObjectMeta: metav1.ObjectMeta{
            Name: generateCrossVPCEniName(podNamespace, podName),
            Labels: map[string]string{
                "pod.namespace": podNamespace,
                "pod.name":      podName,
                "owner":         owner,
            },
        },
        Spec: *spec,
        Status: ccev2.CrossVPCEniStatus{
            EniStatus:           ccev2.EniStatusPending,
            InvolvedContainerID: extractContainerID(owner),
        },
    }
    
    createdEni, err := c.client.CceV2().CrossVPCEnis().Create(ctx, crossVPCEni, metav1.CreateOptions{})
    if err != nil {
        return nil, fmt.Errorf("failed to create CrossVPCEni: %w", err)
    }
    
    // 6. 等待ENI就绪
    readyEni, err := c.waitForENIReady(ctx, createdEni.Name, 5*time.Minute)
    if err != nil {
        return nil, fmt.Errorf("ENI not ready within timeout: %w", err)
    }
    
    // 7. 构造分配结果
    result := &AllocationResult{
        IP:     net.ParseIP(readyEni.Status.PrimaryIPAddress),
        Master: readyEni.Status.MacAddress,
        // 其他必要字段...
    }
    
    return result, nil
}

func (c *crossVPCEniAllocator) parseCrossVPCEniAnnotations(annotations map[string]string) (*ccev2.CrossVPCEniSpec, error) {
    spec := &ccev2.CrossVPCEniSpec{}
    
    // 解析必填字段
    if userID, ok := annotations["cross-vpc-eni.cce.io/userID"]; ok {
        spec.UserID = userID
    } else {
        return nil, fmt.Errorf("missing required annotation: userID")
    }
    
    if subnetID, ok := annotations["cross-vpc-eni.cce.io/subnetID"]; ok {
        spec.SubnetID = subnetID
    } else {
        return nil, fmt.Errorf("missing required annotation: subnetID")
    }
    
    if sgIDs, ok := annotations["cross-vpc-eni.cce.io/securityGroupIDs"]; ok {
        spec.SecurityGroupIDs = strings.Split(sgIDs, ",")
    } else {
        return nil, fmt.Errorf("missing required annotation: securityGroupIDs")
    }
    
    if vpcCIDR, ok := annotations["cross-vpc-eni.cce.io/vpcCidr"]; ok {
        spec.VPCCIDR = vpcCIDR
    } else {
        return nil, fmt.Errorf("missing required annotation: vpcCidr")
    }
    
    // 解析可选字段
    if privateIP, ok := annotations["cross-vpc-eni.cce.io/privateIPAddress"]; ok {
        spec.PrivateIPAddress = privateIP
    }
    
    if delegation, ok := annotations["cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation"]; ok {
        spec.DefaultRouteInterfaceDelegation = delegation
    }
    
    if excludedCidrs, ok := annotations["cross-vpc-eni.cce.io/defaultRouteExcludedCidrs"]; ok {
        spec.DefaultRouteExcludedCidrs = strings.Split(excludedCidrs, ",")
    }
    
    return spec, nil
}

func (c *crossVPCEniAllocator) waitForENIReady(ctx context.Context, eniName string, timeout time.Duration) (*ccev2.CrossVPCEni, error) {
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()
    
    ticker := time.NewTicker(2 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-timeoutCtx.Done():
            return nil, fmt.Errorf("timeout waiting for ENI to be ready")
        case <-ticker.C:
            eni, err := c.client.CceV2().CrossVPCEnis().Get(ctx, eniName, metav1.GetOptions{})
            if err != nil {
                continue
            }
            
            if eni.Status.EniStatus == ccev2.EniStatusInuse && eni.Status.PrimaryIPAddress != "" {
                return eni, nil
            }
            
            if eni.Status.EniStatus == ccev2.EniStatusDeleted {
                return nil, fmt.Errorf("ENI was deleted during allocation")
            }
        }
    }
}

func hasCrossVPCEniAnnotations(annotations map[string]string) bool {
    requiredAnnotations := []string{
        "cross-vpc-eni.cce.io/userID",
        "cross-vpc-eni.cce.io/subnetID",
        "cross-vpc-eni.cce.io/securityGroupIDs",
        "cross-vpc-eni.cce.io/vpcCidr",
    }
    
    for _, annotation := range requiredAnnotations {
        if _, ok := annotations[annotation]; !ok {
            return false
        }
    }
    return true
}
```

## 3. CNI插件示例

### 3.1 CrossVPC-ENI插件主要逻辑

```go
// plugins/crossvpc-eni/main.go
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "net"
    "time"
    
    "github.com/containernetworking/cni/pkg/skel"
    "github.com/containernetworking/cni/pkg/types"
    current "github.com/containernetworking/cni/pkg/types/100"
    "github.com/containernetworking/plugins/pkg/ns"
    "github.com/vishvananda/netlink"
    
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/client"
    plugintypes "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/cni/types"
)

func cmdAdd(args *skel.CmdArgs) error {
    // 1. 解析配置
    cfg, err := loadConf(args.StdinData)
    if err != nil {
        return fmt.Errorf("failed to load config: %w", err)
    }
    
    // 2. 连接CCE Agent
    client, err := client.NewDefaultClient()
    if err != nil {
        return fmt.Errorf("failed to create client: %w", err)
    }
    
    // 3. 分配CrossVPCEni
    ipam, releaseFunc, err := allocateCrossVPCEniWithAgent(client, cfg, args)
    if err != nil {
        return fmt.Errorf("failed to allocate CrossVPCEni: %w", err)
    }
    defer func() {
        if err != nil && releaseFunc != nil {
            releaseFunc(context.Background())
        }
    }()
    
    // 4. 配置网络
    result, err := configureCrossVPCNetwork(args, ipam, cfg)
    if err != nil {
        return fmt.Errorf("failed to configure network: %w", err)
    }
    
    return types.PrintResult(result, current.ImplementedSpecVersion)
}

func configureCrossVPCNetwork(args *skel.CmdArgs, ipam *models.IPAMResponse, cfg *NetConf) (*current.Result, error) {
    // 1. 获取ENI网络接口
    link, err := getCrossVPCEniLink(ipam.Address.MacAddress)
    if err != nil {
        return nil, fmt.Errorf("failed to get ENI link: %w", err)
    }
    
    // 2. 进入容器网络命名空间
    netns, err := ns.GetNS(args.Netns)
    if err != nil {
        return nil, fmt.Errorf("failed to get netns: %w", err)
    }
    defer netns.Close()
    
    // 3. 移动接口到容器命名空间
    err = netlink.LinkSetNsFd(link, int(netns.Fd()))
    if err != nil {
        return nil, fmt.Errorf("failed to move link to netns: %w", err)
    }
    
    // 4. 在容器命名空间内配置网络
    err = netns.Do(func(_ ns.NetNS) error {
        // 重命名接口
        err := netlink.LinkSetName(link, args.IfName)
        if err != nil {
            return fmt.Errorf("failed to rename link: %w", err)
        }
        
        // 启用接口
        err = netlink.LinkSetUp(link)
        if err != nil {
            return fmt.Errorf("failed to set link up: %w", err)
        }
        
        // 配置IP地址
        addr := &netlink.Addr{
            IPNet: &net.IPNet{
                IP:   ipam.Address.IP,
                Mask: ipam.Address.Mask,
            },
        }
        err = netlink.AddrAdd(link, addr)
        if err != nil {
            return fmt.Errorf("failed to add address: %w", err)
        }
        
        // 配置路由
        return configureCrossVPCRoutes(ipam, cfg)
    })
    
    if err != nil {
        return nil, err
    }
    
    // 5. 构造返回结果
    result := &current.Result{
        CNIVersion: current.ImplementedSpecVersion,
        Interfaces: []*current.Interface{{
            Name:    args.IfName,
            Mac:     ipam.Address.MacAddress,
            Sandbox: args.Netns,
        }},
        IPs: []*current.IPConfig{{
            Version:   "4",
            Interface: current.Int(0),
            Address: net.IPNet{
                IP:   ipam.Address.IP,
                Mask: ipam.Address.Mask,
            },
            Gateway: ipam.Address.Gateway,
        }},
    }
    
    return result, nil
}

func configureCrossVPCRoutes(ipam *models.IPAMResponse, cfg *NetConf) error {
    // 配置跨VPC路由规则
    // 1. 默认路由配置
    if ipam.DefaultRouteInterfaceDelegation == "eni" {
        defaultRoute := &netlink.Route{
            Dst: nil, // 默认路由
            Gw:  ipam.Address.Gateway,
        }
        err := netlink.RouteAdd(defaultRoute)
        if err != nil && !os.IsExist(err) {
            return fmt.Errorf("failed to add default route: %w", err)
        }
    }
    
    // 2. 排除路由配置
    for _, excludedCidr := range ipam.DefaultRouteExcludedCidrs {
        _, cidr, err := net.ParseCIDR(excludedCidr)
        if err != nil {
            continue
        }
        
        excludeRoute := &netlink.Route{
            Dst:   cidr,
            Scope: netlink.SCOPE_LINK,
        }
        err = netlink.RouteAdd(excludeRoute)
        if err != nil && !os.IsExist(err) {
            return fmt.Errorf("failed to add exclude route for %s: %w", excludedCidr, err)
        }
    }
    
    return nil
}

func getCrossVPCEniLink(macAddress string) (netlink.Link, error) {
    // 根据MAC地址查找ENI网络接口
    links, err := netlink.LinkList()
    if err != nil {
        return nil, fmt.Errorf("failed to list links: %w", err)
    }
    
    for _, link := range links {
        if link.Attrs().HardwareAddr.String() == macAddress {
            return link, nil
        }
    }
    
    return nil, fmt.Errorf("ENI link with MAC %s not found", macAddress)
}
```

## 4. 配置示例

### 4.1 Pod配置示例

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: crossvpc-test-pod
  annotations:
    cross-vpc-eni.cce.io/userID: "user123456"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123def456"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456,sg-789012"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    cross-vpc-eni.cce.io/privateIPAddress: "*************"
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "eni"
    cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "10.0.0.0/8,**********/12"
spec:
  containers:
  - name: test-container
    image: nginx:latest
    ports:
    - containerPort: 80
```

### 4.2 CNI配置示例

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-eni-network",
  "type": "crossvpc-eni",
  "ifName": "eth0",
  "endpoint": "unix:///var/run/cce-ipam.sock",
  "mtu": 1500,
  "ipam": {
    "type": "crossvpc-eni-ipam"
  }
}
```

这些代码示例展示了CrossVPCEni特性的核心实现逻辑，包括CRD定义、IPAM分配器和CNI插件的关键部分。开发团队可以基于这些示例进行具体的实现工作。
