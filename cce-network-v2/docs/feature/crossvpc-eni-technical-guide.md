# CrossVPCEni 技术实现指南

## 1. 核心代码文件清单

### 1.1 新增文件

| 文件路径 | 功能描述 | 基于模板 |
|---------|---------|---------|
| `pkg/k8s/apis/cce.baidubce.com/v2/crossvpc_eni_types.go` | CrossVPCEni CRD定义 | `cce_eni_types.go` |
| `pkg/ipam/crossvpc_eni_allocator.go` | CrossVPCEni IPAM分配器 | `crd_allocator.go` |
| `pkg/enim/crossvpc_eni_manager.go` | CrossVPCEni管理器 | `enim.go` |
| `plugins/crossvpc-eni/main.go` | CrossVPCEni CNI插件 | `plugins/enim/enim.go` |
| `deploy/cce-network-v2/templates/crd/cce.baidubce.com_crossvpcenis.yaml` | CRD部署文件 | `cce.baidubce.com_enis.yaml` |

### 1.2 修改文件

| 文件路径 | 修改内容 | 修改方式 |
|---------|---------|---------|
| `pkg/ipam/ipam.go` | 添加CrossVPCEni分配器初始化 | 扩展现有switch语句 |
| `pkg/k8s/apis/cce.baidubce.com/client/register.go` | 注册CrossVPCEni CRD | 添加到现有映射表 |
| `pkg/k8s/synced/crd.go` | 同步CrossVPCEni CRD | 扩展现有CRD列表 |
| `pkg/ipam/option/option.go` | 添加CrossVPCEni IPAM选项 | 新增常量定义 |

## 2. 详细实现步骤

### 2.1 步骤1：定义CrossVPCEni CRD

**文件**：`pkg/k8s/apis/cce.baidubce.com/v2/crossvpc_eni_types.go`

```go
package v2

import (
    metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +genclient:nonNamespaced
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +kubebuilder:resource:categories={cce},path="crossvpcenis",scope="Cluster",shortName={cveni}
// +kubebuilder:printcolumn:JSONPath=".spec.userID",description="User ID",name="USER_ID",type=string
// +kubebuilder:printcolumn:JSONPath=".status.eniID",description="ENI ID",name="ENI_ID",type=string
// +kubebuilder:printcolumn:JSONPath=".status.eniStatus",description="ENI Status",name="STATUS",type=string
// +kubebuilder:printcolumn:JSONPath=".spec.boundInstanceID",description="Bound Instance",name="INSTANCE",type=string
// +kubebuilder:printcolumn:JSONPath=".status.vpcID",description="VPC ID",name="VPC",type=string
// +kubebuilder:printcolumn:JSONPath=".metadata.creationTimestamp",description="Create Time",name="CREATETIME",type=date

type CrossVPCEni struct {
    metav1.TypeMeta   `json:",inline"`
    metav1.ObjectMeta `json:"metadata,omitempty"`
    Spec   CrossVPCEniSpec   `json:"spec,omitempty"`
    Status CrossVPCEniStatus `json:"status,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type CrossVPCEniList struct {
    metav1.TypeMeta `json:",inline"`
    metav1.ListMeta `json:"metadata"`
    Items           []CrossVPCEni `json:"items"`
}

type CrossVPCEniSpec struct {
    UserID                           string   `json:"userID"`
    SubnetID                         string   `json:"subnetID"`
    SecurityGroupIDs                 []string `json:"securityGroupIDs"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    PrivateIPAddress                 string   `json:"privateIPAddress,omitempty"`
    BoundInstanceID                  string   `json:"boundInstanceID"`
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

type CrossVPCEniStatus struct {
    EniID               string                    `json:"eniID,omitempty"`
    EniStatus           EniStatus                 `json:"eniStatus"`
    PrimaryIPAddress    string                    `json:"primaryIPAddress,omitempty"`
    MacAddress          string                    `json:"macAddress,omitempty"`
    VPCID               string                    `json:"vpcID,omitempty"`
    InvolvedContainerID string                    `json:"involvedContainerID,omitempty"`
    Conditions          []CrossVPCEniCondition    `json:"conditions,omitempty"`
}

type CrossVPCEniCondition struct {
    Type               CrossVPCEniConditionType `json:"type"`
    Status             metav1.ConditionStatus   `json:"status"`
    LastTransitionTime metav1.Time              `json:"lastTransitionTime"`
    Reason             string                   `json:"reason,omitempty"`
    Message            string                   `json:"message,omitempty"`
}

type CrossVPCEniConditionType string

const (
    CrossVPCEniReady    CrossVPCEniConditionType = "Ready"
    CrossVPCEniAttached CrossVPCEniConditionType = "Attached"
    CrossVPCEniError    CrossVPCEniConditionType = "Error"
)

// EniStatus 复用现有的ENI状态定义
const (
    EniStatusPending   EniStatus = "pending"
    EniStatusCreated   EniStatus = "created"
    EniStatusAttaching EniStatus = "attaching"
    EniStatusInuse     EniStatus = "inuse"
    EniStatusDetaching EniStatus = "detaching"
    EniStatusDetached  EniStatus = "detached"
    EniStatusDeleted   EniStatus = "deleted"
)
```

### 2.2 步骤2：实现CrossVPCEni IPAM分配器

**文件**：`pkg/ipam/crossvpc_eni_allocator.go`

```go
package ipam

import (
    "context"
    "fmt"
    "time"
    
    "github.com/sirupsen/logrus"
    metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
    
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s"
    ccev2 "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s/apis/cce.baidubce.com/v2"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/logging"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/logging/logfields"
)

var crossVPCEniLog = logging.DefaultLogger.WithField(logfields.LogSubsys, "crossvpc-eni-ipam")

type crossVPCEniAllocator struct {
    client k8s.CCEClient
    family Family
}

func newCrossVPCEniAllocator(family Family, c Configuration, owner string, k8sEventReg K8sEventRegister) Allocator {
    return &crossVPCEniAllocator{
        client: k8s.CCEClient(),
        family: family,
    }
}

func (c *crossVPCEniAllocator) Allocate(ctx context.Context, owner string) (*AllocationResult, error) {
    scopedLog := crossVPCEniLog.WithFields(logrus.Fields{
        "owner": owner,
        "family": c.family,
    })
    
    scopedLog.Debug("Allocating CrossVPCEni")
    
    // 1. 解析owner获取Pod信息
    podNamespace, podName, err := parsePodOwner(owner)
    if err != nil {
        return nil, fmt.Errorf("failed to parse pod owner: %w", err)
    }
    
    // 2. 获取Pod对象和注解
    pod, err := k8s.CCEClient().CoreV1().Pods(podNamespace).Get(ctx, podName, metav1.GetOptions{})
    if err != nil {
        return nil, fmt.Errorf("failed to get pod: %w", err)
    }
    
    // 3. 解析CrossVPCEni注解
    spec, err := c.parseCrossVPCEniAnnotations(pod.Annotations)
    if err != nil {
        return nil, fmt.Errorf("failed to parse CrossVPCEni annotations: %w", err)
    }
    
    // 4. 创建CrossVPCEni资源
    crossVPCEni := &ccev2.CrossVPCEni{
        ObjectMeta: metav1.ObjectMeta{
            Name: generateCrossVPCEniName(podNamespace, podName),
            Labels: map[string]string{
                "pod.namespace": podNamespace,
                "pod.name":      podName,
            },
        },
        Spec: *spec,
    }
    
    createdEni, err := c.client.CceV2().CrossVPCEnis().Create(ctx, crossVPCEni, metav1.CreateOptions{})
    if err != nil {
        return nil, fmt.Errorf("failed to create CrossVPCEni: %w", err)
    }
    
    // 5. 等待ENI状态变为Inuse
    err = c.waitForENIReady(ctx, createdEni.Name, 5*time.Minute)
    if err != nil {
        return nil, fmt.Errorf("failed to wait for ENI ready: %w", err)
    }
    
    // 6. 获取最新的ENI状态
    readyEni, err := c.client.CceV2().CrossVPCEnis().Get(ctx, createdEni.Name, metav1.GetOptions{})
    if err != nil {
        return nil, fmt.Errorf("failed to get ready ENI: %w", err)
    }
    
    // 7. 构造分配结果
    result := &AllocationResult{
        IP: net.ParseIP(readyEni.Status.PrimaryIPAddress),
        // 其他字段...
    }
    
    scopedLog.WithField("eni-id", readyEni.Status.EniID).Info("CrossVPCEni allocated successfully")
    return result, nil
}

func (c *crossVPCEniAllocator) Release(ctx context.Context, owner string) error {
    // 实现释放逻辑
    // 删除CrossVPCEni资源，触发云平台清理
    return nil
}

func (c *crossVPCEniAllocator) parseCrossVPCEniAnnotations(annotations map[string]string) (*ccev2.CrossVPCEniSpec, error) {
    // 解析Pod注解，构造CrossVPCEniSpec
    spec := &ccev2.CrossVPCEniSpec{}
    
    // 必填字段验证和解析
    if userID, ok := annotations["cross-vpc-eni.cce.io/userID"]; ok {
        spec.UserID = userID
    } else {
        return nil, fmt.Errorf("missing required annotation: cross-vpc-eni.cce.io/userID")
    }
    
    // 其他字段解析...
    
    return spec, nil
}

func (c *crossVPCEniAllocator) waitForENIReady(ctx context.Context, eniName string, timeout time.Duration) error {
    // 实现等待ENI就绪的逻辑
    // 使用watch机制或轮询检查ENI状态
    return nil
}
```

### 2.3 步骤3：扩展IPAM初始化

**文件**：`pkg/ipam/ipam.go`

```go
// 在现有的NewIPAM函数中添加CrossVPCEni支持
func NewIPAM(nodeAddressing datapath.NodeAddressing, c Configuration, owner string, k8sEventReg K8sEventRegister, mtuConfig MtuConfiguration) *IPAM {
    // ... 现有代码 ...
    
    switch c.IPAMMode() {
    // ... 现有case语句 ...
    
    case ipamOption.IPAMCrossVPCEni:
        log.Info("Initializing CrossVPCEni IPAM")
        if c.IPv4Enabled() {
            ipam.IPv4Allocator = newCrossVPCEniAllocator(IPv4, c, owner, k8sEventReg)
        }
        if c.IPv6Enabled() {
            ipam.IPv6Allocator = newCrossVPCEniAllocator(IPv6, c, owner, k8sEventReg)
        }
    }
    
    // ... 现有代码 ...
}
```

### 2.4 步骤4：添加IPAM选项

**文件**：`pkg/ipam/option/option.go`

```go
const (
    // ... 现有常量 ...
    
    // IPAMCrossVPCEni is the value to select the CrossVPCEni IPAM plugin
    IPAMCrossVPCEni = "crossvpc-eni"
)
```

## 3. CNI插件实现

### 3.1 CrossVPC-ENI插件主文件

**文件**：`plugins/crossvpc-eni/main.go`

```go
package main

import (
    "context"
    "encoding/json"
    "fmt"
    "net"
    "time"
    
    "github.com/containernetworking/cni/pkg/skel"
    "github.com/containernetworking/cni/pkg/types"
    current "github.com/containernetworking/cni/pkg/types/100"
    "github.com/containernetworking/cni/pkg/version"
    "github.com/containernetworking/plugins/pkg/ns"
    "github.com/sirupsen/logrus"
    "github.com/vishvananda/netlink"
    
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/client"
    plugintypes "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/cni/types"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/logging"
)

var logger *logrus.Entry

func main() {
    skel.PluginMain(cmdAdd, cmdCheck, cmdDel, version.All, "crossvpc-eni")
}

func cmdAdd(args *skel.CmdArgs) error {
    logging.SetupCNILogging("crossvpc-eni", true)
    logger = logging.DefaultLogger.WithFields(logrus.Fields{
        "plugin": "crossvpc-eni",
        "mode":   "ADD",
    })
    
    logger.Info("CrossVPC-ENI CNI ADD started")
    
    // 1. 解析CNI配置
    cfg, err := loadConf(args.StdinData)
    if err != nil {
        return fmt.Errorf("failed to load CNI config: %w", err)
    }
    
    // 2. 连接到CCE Agent
    client, err := client.NewDefaultClient()
    if err != nil {
        return fmt.Errorf("failed to create CCE client: %w", err)
    }
    
    // 3. 分配CrossVPCEni
    ipam, releaseFunc, err := allocateCrossVPCEniWithAgent(client, cfg, args)
    if err != nil {
        return fmt.Errorf("failed to allocate CrossVPCEni: %w", err)
    }
    defer func() {
        if err != nil && releaseFunc != nil {
            releaseFunc(context.Background())
        }
    }()
    
    // 4. 配置网络
    result, err := configureCrossVPCNetwork(args, ipam, cfg)
    if err != nil {
        return fmt.Errorf("failed to configure network: %w", err)
    }
    
    logger.Info("CrossVPC-ENI CNI ADD completed successfully")
    return types.PrintResult(result, current.ImplementedSpecVersion)
}

func cmdDel(args *skel.CmdArgs) error {
    // 实现删除逻辑
    return nil
}

func cmdCheck(args *skel.CmdArgs) error {
    // 实现检查逻辑
    return nil
}

type NetConf struct {
    types.NetConf
    MTU      int    `json:"mtu"`
    Endpoint string `json:"endpoint"`
}

func loadConf(bytes []byte) (*NetConf, error) {
    conf := &NetConf{}
    if err := json.Unmarshal(bytes, conf); err != nil {
        return nil, fmt.Errorf("failed to unmarshal config: %w", err)
    }
    return conf, nil
}
```

## 4. 部署配置

### 4.1 CRD部署文件

**文件**：`deploy/cce-network-v2/templates/crd/cce.baidubce.com_crossvpcenis.yaml`

基于现有的`cce.baidubce.com_enis.yaml`模板，修改相应的字段定义和名称。

### 4.2 ConfigMap配置

在现有的ConfigMap中添加CrossVPCEni相关配置选项：

```yaml
data:
  enable-crossvpc-eni: "false"
  crossvpc-eni-timeout: "300s"
  crossvpc-eni-gc-interval: "5m"
```

## 5. 测试验证

### 5.1 单元测试

为每个新增组件编写完整的单元测试，确保功能正确性。

### 5.2 集成测试

验证CrossVPCEni与现有组件的集成效果，确保不影响现有功能。

### 5.3 端到端测试

创建测试Pod，验证完整的CrossVPCEni分配和网络配置流程。

这个技术实现指南提供了详细的代码实现步骤和关键技术点，可以作为开发团队实施CrossVPCEni特性的具体指导文档。
