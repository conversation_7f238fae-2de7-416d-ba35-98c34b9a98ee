# CrossVPCEni 基于PSTS模式的代码实现示例

## 1. 核心数据结构

### 1.1 CCEEndpoint扩展

```go
// pkg/k8s/apis/cce.baidubce.com/v2/cce_endpoint_types.go
type IPAllocation struct {
    Type            IPAllocType               `json:"type,omitempty"`
    Pool            string                    `json:"pool,omitempty"`
    PSTSName        string                    `json:"pstsName,omitempty"`
    NodeName        string                    `json:"nodeName,omitempty"`
    ReleaseStrategy ReleaseStrategy           `json:"releaseStrategy,omitempty"`
    CrossVPCConfig  *CrossVPCAllocationConfig `json:"crossVPCConfig,omitempty"` // 新增
}

type CrossVPCAllocationConfig struct {
    UserID                           string   `json:"userID"`
    SubnetID                         string   `json:"subnetID"`
    SecurityGroupIDs                 []string `json:"securityGroupIDs"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    Mode                             string   `json:"mode"` // "primary" | "secondary"
    PrivateIPAddress                 string   `json:"privateIPAddress,omitempty"`
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

// pkg/k8s/apis/cce.baidubce.com/v2/psts.go
const (
    IPAllocTypeNil                   IPAllocType = ""
    IPAllocTypeElastic               IPAllocType = "Elastic"
    IPAllocTypeFixed                 IPAllocType = "Fixed"
    IPAllocTypeENIPrimary            IPAllocType = "PrimaryENI"
    IPAllocTypeCrossVPCPrimary       IPAllocType = "CrossVPCPrimary"   // 新增
    IPAllocTypeCrossVPCSecondary     IPAllocType = "CrossVPCSecondary" // 新增
)
```

## 2. Agent侧实现

### 2.1 CrossVPC IPAM检测和处理

```go
// pkg/endpoint/agent_endpoint_allocator.go
func (e *endpointAllocator) allocateIP(ctx context.Context, logEntry *logrus.Entry, 
    containerID string, family ipam.Family, owner, netns string, 
    psts *ccev2.PodSubnetTopologySpread, pod *corev1.Pod, isFixedPod bool) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    // 检查是否为CrossVPC请求
    if isCrossVPCRequest(pod) {
        logEntry.Info("detected CrossVPC request")
        return e.allocateCrossVPCIP(ctx, logEntry, containerID, family, owner, netns, pod)
    }
    
    // 现有的PSTS和其他分配逻辑
    if psts != nil {
        return e.allocatePSTSIP(ctx, logEntry, containerID, family, owner, netns, psts, pod)
    }
    
    // 其他分配逻辑...
    return e.allocateRegularIP(ctx, logEntry, containerID, family, owner, netns, pod, isFixedPod)
}

func isCrossVPCRequest(pod *corev1.Pod) bool {
    enabled, ok := pod.Annotations["cross-vpc-eni.cce.io/enabled"]
    return ok && enabled == "true"
}

func (e *endpointAllocator) allocateCrossVPCIP(ctx context.Context, logEntry *logrus.Entry,
    containerID string, family ipam.Family, owner, netns string, pod *corev1.Pod) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    logEntry = logEntry.WithField("step", "allocateCrossVPCIP")
    
    // 1. 解析CrossVPC配置
    crossVPCConfig, err := parseCrossVPCAnnotations(pod.Annotations)
    if err != nil {
        logEntry.WithError(err).Error("failed to parse CrossVPC annotations")
        return nil, nil, fmt.Errorf("failed to parse CrossVPC annotations: %w", err)
    }
    
    logEntry = logEntry.WithFields(logrus.Fields{
        "crossvpc-mode":     crossVPCConfig.Mode,
        "crossvpc-userID":   crossVPCConfig.UserID,
        "crossvpc-subnetID": crossVPCConfig.SubnetID,
    })
    
    // 2. 创建CCEEndpoint模板
    ep := endpoint.NewEndpointTemplate(containerID, netns, pod)
    
    // 3. 设置CrossVPC分配类型
    allocType := ccev2.IPAllocTypeCrossVPCSecondary // 默认共享模式
    if crossVPCConfig.Mode == "primary" {
        allocType = ccev2.IPAllocTypeCrossVPCPrimary
    }
    
    ep.Spec.Network.IPAllocation = &ccev2.IPAllocation{
        Type:            allocType,
        NodeName:        nodeTypes.GetName(),
        ReleaseStrategy: ccev2.ReleaseStrategyTTL,
        CrossVPCConfig:  crossVPCConfig,
    }
    
    logEntry.WithField("allocType", allocType).Info("creating CCEEndpoint for CrossVPC")
    
    // 4. 创建endpoint，触发operator处理
    ep, err = e.cceEndpointClient.CCEEndpoints(ep.Namespace).Create(ctx, ep, metav1.CreateOptions{})
    if err != nil {
        logEntry.WithError(err).Error("failed to create CCEEndpoint")
        return nil, nil, fmt.Errorf("failed to create CCEEndpoint: %w", err)
    }
    
    // 5. 等待operator分配完成
    logEntry.Info("waiting for CrossVPC IP allocation")
    return e.waitForCrossVPCIPAllocation(ctx, ep, logEntry)
}

func parseCrossVPCAnnotations(annotations map[string]string) (*ccev2.CrossVPCAllocationConfig, error) {
    config := &ccev2.CrossVPCAllocationConfig{}
    
    // 解析必填字段
    requiredFields := map[string]*string{
        "cross-vpc-eni.cce.io/userID":           &config.UserID,
        "cross-vpc-eni.cce.io/subnetID":         &config.SubnetID,
        "cross-vpc-eni.cce.io/vpcCidr":          &config.VPCCIDR,
    }
    
    for annotation, field := range requiredFields {
        if value, ok := annotations[annotation]; ok {
            *field = value
        } else {
            return nil, fmt.Errorf("missing required annotation: %s", annotation)
        }
    }
    
    // 解析SecurityGroupIDs
    if sgIDs, ok := annotations["cross-vpc-eni.cce.io/securityGroupIDs"]; ok {
        config.SecurityGroupIDs = strings.Split(sgIDs, ",")
        // 清理空白字符
        for i, sgID := range config.SecurityGroupIDs {
            config.SecurityGroupIDs[i] = strings.TrimSpace(sgID)
        }
    } else {
        return nil, fmt.Errorf("missing required annotation: cross-vpc-eni.cce.io/securityGroupIDs")
    }
    
    // 解析可选字段
    config.Mode = annotations["cross-vpc-eni.cce.io/mode"]
    if config.Mode == "" {
        config.Mode = "secondary" // 默认共享模式
    }
    
    config.PrivateIPAddress = annotations["cross-vpc-eni.cce.io/privateIPAddress"]
    config.DefaultRouteInterfaceDelegation = annotations["cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation"]
    
    if excludedCidrs, ok := annotations["cross-vpc-eni.cce.io/defaultRouteExcludedCidrs"]; ok {
        config.DefaultRouteExcludedCidrs = strings.Split(excludedCidrs, ",")
        // 清理空白字符
        for i, cidr := range config.DefaultRouteExcludedCidrs {
            config.DefaultRouteExcludedCidrs[i] = strings.TrimSpace(cidr)
        }
    }
    
    return config, nil
}

func (e *endpointAllocator) waitForCrossVPCIPAllocation(ctx context.Context, ep *ccev2.CCEEndpoint, logEntry *logrus.Entry) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    // 等待超时设置
    timeout := 5 * time.Minute
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()
    
    // 轮询检查endpoint状态
    ticker := time.NewTicker(2 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-timeoutCtx.Done():
            logEntry.Error("timeout waiting for CrossVPC IP allocation")
            return nil, nil, fmt.Errorf("timeout waiting for CrossVPC IP allocation")
            
        case <-ticker.C:
            // 获取最新的endpoint状态
            currentEP, err := e.cceEndpointClient.CCEEndpoints(ep.Namespace).Get(ctx, ep.Name, metav1.GetOptions{})
            if err != nil {
                logEntry.WithError(err).Debug("failed to get CCEEndpoint")
                continue
            }
            
            // 检查是否分配成功
            if len(currentEP.Status.Networking.Addressing) > 0 {
                logEntry.Info("CrossVPC IP allocation completed")
                return e.convertAddressingToIPAMResponse(currentEP.Status.Networking.Addressing)
            }
            
            // 检查是否有错误
            if currentEP.Status.State == ccev2.EndpointStateFailed {
                logEntry.Error("CrossVPC IP allocation failed")
                return nil, nil, fmt.Errorf("CrossVPC IP allocation failed: %s", currentEP.Status.Log)
            }
            
            logEntry.Debug("still waiting for CrossVPC IP allocation")
        }
    }
}

func (e *endpointAllocator) convertAddressingToIPAMResponse(addressing []*ccev2.AddressPair) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    for _, addr := range addressing {
        response := &models.IPAMAddressResponse{
            Address: &models.AddressPair{
                IPV4: addr.IP,
            },
            HostAddressing: &models.NodeAddressing{
                IPV4: &models.NodeAddressingElement{
                    Enabled: true,
                },
            },
            Interface: addr.Interface,
            Subnet:    addr.Subnet,
        }
        
        if addr.Family == ccev2.IPv4Family {
            ipv4Result = response
        } else if addr.Family == ccev2.IPv6Family {
            ipv6Result = response
        }
    }
    
    if ipv4Result == nil && ipv6Result == nil {
        return nil, nil, fmt.Errorf("no valid IP address found in addressing")
    }
    
    return ipv4Result, ipv6Result, nil
}
```

## 3. Operator侧实现

### 3.1 CrossVPC分配器提供者

```go
// pkg/endpoint/operator_crossvpc_manager_provider.go
package endpoint

import (
    "context"
    "fmt"
    "time"
    
    ccev2 "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s/apis/cce.baidubce.com/v2"
    "github.com/sirupsen/logrus"
)

type crossVPCAllocatorProvider struct {
    *EndpointManager
}

func (provider *crossVPCAllocatorProvider) AllocateIP(ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint) error {
    var (
        owner          = resource.Namespace + "/" + resource.Name
        status         = &resource.Status
        crossVPCConfig = resource.Spec.Network.IPAllocation.CrossVPCConfig
        allocType      = resource.Spec.Network.IPAllocation.Type
    )
    
    if crossVPCConfig == nil {
        return fmt.Errorf("CrossVPC config is required for CrossVPC allocation")
    }
    
    log = log.WithFields(logrus.Fields{
        "crossvpc-mode":     crossVPCConfig.Mode,
        "crossvpc-type":     allocType,
        "crossvpc-userID":   crossVPCConfig.UserID,
        "crossvpc-subnetID": crossVPCConfig.SubnetID,
        "endpoint":          owner,
    })
    log.Info("start allocate CrossVPC ENI")
    
    // 记录开始时间
    startTime := time.Now()
    
    // 获取远程操作接口
    operation, err := provider.directIPAllocator.NodeEndpoint(resource)
    if err != nil {
        log.WithError(err).Error("failed to get node endpoint")
        return fmt.Errorf("failed to get node endpoint: %w", err)
    }
    
    // 根据分配类型选择不同的处理逻辑
    switch allocType {
    case ccev2.IPAllocTypeCrossVPCPrimary:
        err = provider.allocateCrossVPCPrimary(ctx, log, resource, operation)
    case ccev2.IPAllocTypeCrossVPCSecondary:
        err = provider.allocateCrossVPCSecondary(ctx, log, resource, operation)
    default:
        err = fmt.Errorf("unsupported CrossVPC allocation type: %s", allocType)
    }
    
    // 记录分配结果
    duration := time.Since(startTime)
    if err != nil {
        log.WithError(err).WithField("duration", duration).Error("CrossVPC ENI allocation failed")
        // 记录监控指标
        RecordCrossVPCEniAllocation(resource.Spec.Network.IPAllocation.NodeName, 
            crossVPCConfig.Mode, crossVPCConfig.UserID, "failed", duration)
    } else {
        log.WithField("duration", duration).Info("CrossVPC ENI allocation succeeded")
        // 记录监控指标
        RecordCrossVPCEniAllocation(resource.Spec.Network.IPAllocation.NodeName, 
            crossVPCConfig.Mode, crossVPCConfig.UserID, "success", duration)
    }
    
    return err
}

// allocateCrossVPCPrimary 分配独占模式的CrossVPC ENI
func (provider *crossVPCAllocatorProvider) allocateCrossVPCPrimary(
    ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint, 
    operation DirectEndpointOperation) error {
    
    var (
        status         = &resource.Status
        crossVPCConfig = resource.Spec.Network.IPAllocation.CrossVPCConfig
        owner          = resource.Namespace + "/" + resource.Name
    )
    
    log = log.WithField("step", "allocate crossvpc primary eni")
    
    // 构造分配动作
    action := &DirectIPAction{
        NodeName:    resource.Spec.Network.IPAllocation.NodeName,
        Owner:       owner,
        SubnetID:    crossVPCConfig.SubnetID,
        RequestedIP: crossVPCConfig.PrivateIPAddress,
        CrossVPCConfig: &DirectIPCrossVPCConfig{
            UserID:           crossVPCConfig.UserID,
            SecurityGroupIDs: crossVPCConfig.SecurityGroupIDs,
            VPCCIDR:          crossVPCConfig.VPCCIDR,
            Mode:             "primary",
            DefaultRouteInterfaceDelegation: crossVPCConfig.DefaultRouteInterfaceDelegation,
            DefaultRouteExcludedCidrs:       crossVPCConfig.DefaultRouteExcludedCidrs,
        },
    }
    
    log = log.WithField("action", fmt.Sprintf("%+v", action))
    
    // 调用远程分配
    err := operation.AllocateCrossVPCENI(ctx, action)
    if err != nil {
        log.WithError(err).Error("failed to allocate CrossVPC primary ENI")
        return fmt.Errorf("failed to allocate CrossVPC primary ENI: %w", err)
    }
    
    log.WithField("addressing", action.Addressing).Info("CrossVPC primary ENI allocated successfully")
    
    // 更新endpoint状态
    status.Networking.Addressing = action.Addressing
    status.NodeSelectorRequirement = action.NodeSelectorRequirement
    return nil
}

// allocateCrossVPCSecondary 分配共享模式的CrossVPC ENI
func (provider *crossVPCAllocatorProvider) allocateCrossVPCSecondary(
    ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint,
    operation DirectEndpointOperation) error {
    
    var (
        status         = &resource.Status
        crossVPCConfig = resource.Spec.Network.IPAllocation.CrossVPCConfig
        owner          = resource.Namespace + "/" + resource.Name
    )
    
    log = log.WithField("step", "allocate crossvpc secondary ip")
    
    // 如果已经有地址，则复用（重启恢复场景）
    if len(status.Networking.Addressing) > 0 {
        log.Info("reuse existing CrossVPC secondary IP")
        return nil
    }
    
    // 构造分配动作
    action := &DirectIPAction{
        NodeName: resource.Spec.Network.IPAllocation.NodeName,
        Owner:    owner,
        SubnetID: crossVPCConfig.SubnetID,
        CrossVPCConfig: &DirectIPCrossVPCConfig{
            UserID:           crossVPCConfig.UserID,
            SecurityGroupIDs: crossVPCConfig.SecurityGroupIDs,
            VPCCIDR:          crossVPCConfig.VPCCIDR,
            Mode:             "secondary",
            DefaultRouteInterfaceDelegation: crossVPCConfig.DefaultRouteInterfaceDelegation,
            DefaultRouteExcludedCidrs:       crossVPCConfig.DefaultRouteExcludedCidrs,
        },
    }
    
    log = log.WithField("action", fmt.Sprintf("%+v", action))
    
    // 调用远程分配
    err := operation.AllocateCrossVPCENI(ctx, action)
    if err != nil {
        log.WithError(err).Error("failed to allocate CrossVPC secondary IP")
        return fmt.Errorf("failed to allocate CrossVPC secondary IP: %w", err)
    }
    
    log.WithField("addressing", action.Addressing).Info("CrossVPC secondary IP allocated successfully")
    
    // 更新endpoint状态
    status.Networking.Addressing = action.Addressing
    status.NodeSelectorRequirement = action.NodeSelectorRequirement
    return nil
}

var _ EndpointMunalAllocatorProvider = &crossVPCAllocatorProvider{}
```

## 4. DirectIPAction扩展

```go
// pkg/endpoint/operator_endpoint_manager.go
type DirectIPAction struct {
    // 现有字段
    NodeName                 string                          `json:"nodeName,omitempty"`
    Owner                    string                          `json:"owner,omitempty"`
    SubnetID                 string                          `json:"subnetID,omitempty"`
    Interface                string                          `json:"interface,omitempty"`
    Addressing               []*ccev2.AddressPair            `json:"addressing,omitempty"`
    NodeSelectorRequirement  []ccev2.NodeSelectorRequirement `json:"nodeSelectorRequirement,omitempty"`
    RequestedIP              string                          `json:"requestedIP,omitempty"`
    
    // 新增CrossVPC字段
    CrossVPCConfig           *DirectIPCrossVPCConfig         `json:"crossVPCConfig,omitempty"`
}

type DirectIPCrossVPCConfig struct {
    UserID                           string   `json:"userID"`
    SecurityGroupIDs                 []string `json:"securityGroupIDs"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    Mode                             string   `json:"mode"` // "primary" | "secondary"
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

// DirectEndpointOperation 接口扩展
type DirectEndpointOperation interface {
    // 现有方法
    AllocateIP(ctx context.Context, action *DirectIPAction) error
    ReleaseIP(ctx context.Context, action *DirectIPAction) error
    FilterAvailableSubnetIds(subnetIDs []string, num int) []*BceSubnet
    
    // 新增CrossVPC方法
    AllocateCrossVPCENI(ctx context.Context, action *DirectIPAction) error
    ReleaseCrossVPCENI(ctx context.Context, action *DirectIPAction) error
}
```

## 5. EndpointManager集成

```go
// pkg/endpoint/operator_endpoint_manager.go
func NewEndpointManager(getterUpdater CCEEndpointGetterUpdater, reuseIPImplement DirectIPAllocator) *EndpointManager {
    manager := &EndpointManager{
        directIPAllocator:      reuseIPImplement,
        fixedIPPoolEndpointMap: make(map[string]map[string]*ccev2.CCEEndpoint),
        localPool:              newLocalPool(),
        remoteIPPool:           make(map[string]ipamTypes.AllocationMap),
        
        k8sAPI:        getterUpdater,
        pstsLister:    k8s.CCEClient().Informers.Cce().V2().PodSubnetTopologySpreads().Lister(),
        sbnLister:     k8s.CCEClient().Informers.Cce().V1().Subnets().Lister(),
        nrsLister:     k8s.CCEClient().Informers.Cce().V2().NetResourceSets().Lister(),
        eventRecorder: k8s.EventBroadcaster().NewRecorder(scheme.Scheme, corev1.EventSource{Component: operatorName}),
    }
    
    // 初始化分配器提供者
    manager.fixedIPProvider = &fixedIPAllocatorProvider{manager}
    manager.pstsAllocatorProvider = &pstsAllocatorProvider{manager}
    manager.crossVPCAllocatorProvider = &crossVPCAllocatorProvider{manager} // 新增
    
    return manager
}

func (manager *EndpointManager) Update(resource *ccev2.CCEEndpoint) error {
    // 检查是否为管理的endpoint类型
    if !IsFixedIPEndpoint(resource) && !IsPSTSEndpoint(resource) && !IsCrossVPCEndpoint(resource) {
        return nil
    }
    
    // 创建日志上下文
    logEntry := log.WithFields(logrus.Fields{
        "namespace": resource.Namespace,
        "name":      resource.Name,
        "step":      "Update",
    })
    
    ctx := context.Background()
    newObj := resource.DeepCopy()
    
    var err error
    
    // 根据类型分发到不同的分配器
    if IsCrossVPCEndpoint(resource) {
        logEntry.Info("processing CrossVPC endpoint")
        err = manager.crossVPCAllocatorProvider.AllocateIP(ctx, logEntry, newObj)
    } else if IsPSTSEndpoint(resource) {
        logEntry.Info("processing PSTS endpoint")
        err = manager.pstsAllocatorProvider.AllocateIP(ctx, logEntry, newObj)
    } else if IsFixedIPEndpoint(resource) {
        logEntry.Info("processing fixed IP endpoint")
        err = manager.fixedIPProvider.AllocateIP(ctx, logEntry, newObj)
    }
    
    // 更新endpoint状态
    if err != nil {
        logEntry.WithError(err).Error("failed to allocate IP")
        newObj.Status.State = ccev2.EndpointStateFailed
        newObj.Status.Log = err.Error()
    } else {
        logEntry.Info("IP allocation succeeded")
        newObj.Status.State = ccev2.EndpointStateReady
    }
    
    // 保存更新
    _, updateErr := manager.k8sAPI.Update(newObj)
    if updateErr != nil {
        logEntry.WithError(updateErr).Error("failed to update endpoint")
        return updateErr
    }
    
    return err
}

// IsCrossVPCEndpoint 判断是否为CrossVPC endpoint
func IsCrossVPCEndpoint(resource *ccev2.CCEEndpoint) bool {
    if resource.Spec.Network.IPAllocation == nil {
        return false
    }
    allocType := resource.Spec.Network.IPAllocation.Type
    return allocType == ccev2.IPAllocTypeCrossVPCPrimary || allocType == ccev2.IPAllocTypeCrossVPCSecondary
}
```

## 6. 云平台适配器详细实现

### 6.1 DirectEndpointOperation接口扩展

```go
// pkg/ipam/allocator/privatecloudbase/privatecloudbase.go
func (p *PrivateCloudBase) AllocateCrossVPCENI(ctx context.Context, action *DirectIPAction) error {
    crossVPCConfig := action.CrossVPCConfig
    if crossVPCConfig == nil {
        return fmt.Errorf("CrossVPC config is required")
    }

    log := p.log.WithFields(logrus.Fields{
        "owner":    action.Owner,
        "mode":     crossVPCConfig.Mode,
        "userID":   crossVPCConfig.UserID,
        "subnetID": action.SubnetID,
        "nodeID":   p.instanceID,
    })

    log.Info("start allocate CrossVPC ENI")

    switch crossVPCConfig.Mode {
    case "primary":
        return p.allocateCrossVPCPrimaryENI(ctx, action, log)
    case "secondary":
        return p.allocateCrossVPCSecondaryENI(ctx, action, log)
    default:
        return fmt.Errorf("unsupported CrossVPC mode: %s", crossVPCConfig.Mode)
    }
}

func (p *PrivateCloudBase) ReleaseCrossVPCENI(ctx context.Context, action *DirectIPAction) error {
    crossVPCConfig := action.CrossVPCConfig
    if crossVPCConfig == nil {
        return fmt.Errorf("CrossVPC config is required for release")
    }

    log := p.log.WithFields(logrus.Fields{
        "owner":    action.Owner,
        "mode":     crossVPCConfig.Mode,
        "userID":   crossVPCConfig.UserID,
    })

    log.Info("start release CrossVPC ENI")

    switch crossVPCConfig.Mode {
    case "primary":
        return p.releaseCrossVPCPrimaryENI(ctx, action, log)
    case "secondary":
        return p.releaseCrossVPCSecondaryENI(ctx, action, log)
    default:
        return fmt.Errorf("unsupported CrossVPC mode: %s", crossVPCConfig.Mode)
    }
}
```

### 6.2 独占模式详细实现

```go
func (p *PrivateCloudBase) allocateCrossVPCPrimaryENI(ctx context.Context, action *DirectIPAction, log *logrus.Entry) error {
    crossVPCConfig := action.CrossVPCConfig

    // 1. 构造创建ENI的参数
    createArgs := &enisdk.CreateEniArgs{
        Name:             p.generateCrossVPCEniName(action.Owner, "primary"),
        SubnetId:         action.SubnetID,
        InstanceId:       p.instanceID,
        SecurityGroupIds: crossVPCConfig.SecurityGroupIDs,
        Description:      fmt.Sprintf("CrossVPC Primary ENI for %s (UserID: %s)", action.Owner, crossVPCConfig.UserID),
        Tags: []enisdk.TagModel{
            {TagKey: "CrossVPC", TagValue: "true"},
            {TagKey: "Mode", TagValue: "primary"},
            {TagKey: "UserID", TagValue: crossVPCConfig.UserID},
            {TagKey: "Owner", TagValue: action.Owner},
            {TagKey: "CreatedBy", TagValue: "CCE-CNI"},
        },
    }

    // 2. 如果指定了IP，则使用指定IP
    if action.RequestedIP != "" {
        log.WithField("requestedIP", action.RequestedIP).Info("using requested IP for CrossVPC primary ENI")
        createArgs.PrivateIpSet = []enisdk.PrivateIp{{
            Primary:          true,
            PrivateIPAddress: action.RequestedIP,
        }}
    }

    log.WithField("createArgs", createArgs).Debug("creating CrossVPC primary ENI")

    // 3. 调用云平台API创建ENI
    eniID, err := p.bceClient.CreateENI(ctx, createArgs)
    if err != nil {
        log.WithError(err).Error("failed to create CrossVPC primary ENI")
        return fmt.Errorf("failed to create CrossVPC primary ENI: %w", err)
    }

    log.WithField("eniID", eniID).Info("CrossVPC primary ENI created, waiting for ready")

    // 4. 等待ENI就绪
    eni, err := p.waitForENIReady(ctx, eniID, 5*time.Minute)
    if err != nil {
        log.WithError(err).Error("CrossVPC primary ENI not ready within timeout")
        // 清理已创建的ENI
        p.cleanupFailedENI(ctx, eniID, log)
        return fmt.Errorf("CrossVPC primary ENI not ready: %w", err)
    }

    // 5. 附加ENI到实例
    err = p.attachENIToInstance(ctx, eniID, log)
    if err != nil {
        log.WithError(err).Error("failed to attach CrossVPC primary ENI to instance")
        // 清理已创建的ENI
        p.cleanupFailedENI(ctx, eniID, log)
        return fmt.Errorf("failed to attach CrossVPC primary ENI: %w", err)
    }

    // 6. 获取网关信息
    gateway, err := p.getSubnetGateway(ctx, action.SubnetID)
    if err != nil {
        log.WithError(err).Warn("failed to get subnet gateway, using default")
        gateway = p.calculateDefaultGateway(eni.PrimaryIP)
    }

    // 7. 构造返回结果
    action.Addressing = []*ccev2.AddressPair{{
        Family:    ccev2.IPv4Family,
        IP:        eni.PrimaryIP,
        Subnet:    action.SubnetID,
        VPCID:     crossVPCConfig.VPCCIDR,
        Interface: eniID,
        Gateway:   gateway,
    }}

    // 8. 设置节点选择器要求（确保Pod调度到正确的节点）
    action.NodeSelectorRequirement = []ccev2.NodeSelectorRequirement{{
        Key:      "kubernetes.io/hostname",
        Operator: ccev2.NodeSelectorOpIn,
        Values:   []string{p.nodeName},
    }}

    log.WithFields(logrus.Fields{
        "eniID":     eniID,
        "primaryIP": eni.PrimaryIP,
        "gateway":   gateway,
    }).Info("CrossVPC primary ENI allocated successfully")

    return nil
}

func (p *PrivateCloudBase) waitForENIReady(ctx context.Context, eniID string, timeout time.Duration) (*ENIInfo, error) {
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()

    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-timeoutCtx.Done():
            return nil, fmt.Errorf("timeout waiting for ENI %s to be ready", eniID)
        case <-ticker.C:
            eni, err := p.bceClient.GetENI(ctx, eniID)
            if err != nil {
                p.log.WithError(err).Debug("failed to get ENI status")
                continue
            }

            if eni.Status == "available" && eni.PrimaryIP != "" {
                return eni, nil
            }

            if eni.Status == "failed" {
                return nil, fmt.Errorf("ENI %s creation failed", eniID)
            }

            p.log.WithFields(logrus.Fields{
                "eniID":  eniID,
                "status": eni.Status,
            }).Debug("ENI not ready yet, continuing to wait")
        }
    }
}

func (p *PrivateCloudBase) attachENIToInstance(ctx context.Context, eniID string, log *logrus.Entry) error {
    // 检查ENI是否已经附加
    eni, err := p.bceClient.GetENI(ctx, eniID)
    if err != nil {
        return fmt.Errorf("failed to get ENI info: %w", err)
    }

    if eni.InstanceID == p.instanceID {
        log.Info("ENI already attached to instance")
        return nil
    }

    // 附加ENI到实例
    err = p.bceClient.AttachENI(ctx, &enisdk.AttachEniArgs{
        EniId:      eniID,
        InstanceId: p.instanceID,
    })
    if err != nil {
        return fmt.Errorf("failed to attach ENI to instance: %w", err)
    }

    // 等待附加完成
    return p.waitForENIAttached(ctx, eniID, 2*time.Minute)
}

func (p *PrivateCloudBase) waitForENIAttached(ctx context.Context, eniID string, timeout time.Duration) error {
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()

    ticker := time.NewTicker(3 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-timeoutCtx.Done():
            return fmt.Errorf("timeout waiting for ENI %s to be attached", eniID)
        case <-ticker.C:
            eni, err := p.bceClient.GetENI(ctx, eniID)
            if err != nil {
                p.log.WithError(err).Debug("failed to get ENI status during attach")
                continue
            }

            if eni.InstanceID == p.instanceID && eni.Status == "inuse" {
                p.log.WithField("eniID", eniID).Info("ENI attached successfully")
                return nil
            }

            p.log.WithFields(logrus.Fields{
                "eniID":      eniID,
                "status":     eni.Status,
                "instanceID": eni.InstanceID,
            }).Debug("ENI not attached yet, continuing to wait")
        }
    }
}

func (p *PrivateCloudBase) cleanupFailedENI(ctx context.Context, eniID string, log *logrus.Entry) {
    log.WithField("eniID", eniID).Info("cleaning up failed ENI")

    // 先尝试分离ENI
    err := p.bceClient.DetachENI(ctx, &enisdk.DetachEniArgs{
        EniId:      eniID,
        InstanceId: p.instanceID,
    })
    if err != nil {
        log.WithError(err).Warn("failed to detach failed ENI")
    } else {
        // 等待分离完成
        time.Sleep(10 * time.Second)
    }

    // 删除ENI
    err = p.bceClient.DeleteENI(ctx, eniID)
    if err != nil {
        log.WithError(err).Error("failed to delete failed ENI")
    } else {
        log.WithField("eniID", eniID).Info("failed ENI cleaned up successfully")
    }
}
```

### 6.3 共享模式详细实现

```go
func (p *PrivateCloudBase) allocateCrossVPCSecondaryENI(ctx context.Context, action *DirectIPAction, log *logrus.Entry) error {
    crossVPCConfig := action.CrossVPCConfig

    // 1. 查找或创建共享的CrossVPC ENI
    eni, err := p.findOrCreateSharedCrossVPCENI(ctx, action, log)
    if err != nil {
        return fmt.Errorf("failed to find or create shared CrossVPC ENI: %w", err)
    }

    log.WithField("sharedEniID", eni.ID).Info("found or created shared CrossVPC ENI")

    // 2. 为ENI分配辅助IP
    secondaryIP, err := p.allocateSecondaryIPForENI(ctx, eni.ID, action.SubnetID, log)
    if err != nil {
        return fmt.Errorf("failed to allocate secondary IP for CrossVPC ENI: %w", err)
    }

    // 3. 获取网关信息
    gateway, err := p.getSubnetGateway(ctx, action.SubnetID)
    if err != nil {
        log.WithError(err).Warn("failed to get subnet gateway, using default")
        gateway = p.calculateDefaultGateway(secondaryIP)
    }

    // 4. 构造返回结果
    action.Addressing = []*ccev2.AddressPair{{
        Family:    ccev2.IPv4Family,
        IP:        secondaryIP,
        Subnet:    action.SubnetID,
        VPCID:     crossVPCConfig.VPCCIDR,
        Interface: eni.ID,
        Gateway:   gateway,
    }}

    // 5. 设置节点选择器要求
    action.NodeSelectorRequirement = []ccev2.NodeSelectorRequirement{{
        Key:      "kubernetes.io/hostname",
        Operator: ccev2.NodeSelectorOpIn,
        Values:   []string{p.nodeName},
    }}

    log.WithFields(logrus.Fields{
        "eniID":       eni.ID,
        "secondaryIP": secondaryIP,
        "gateway":     gateway,
    }).Info("CrossVPC secondary IP allocated successfully")

    return nil
}

func (p *PrivateCloudBase) findOrCreateSharedCrossVPCENI(ctx context.Context, action *DirectIPAction, log *logrus.Entry) (*ENIInfo, error) {
    crossVPCConfig := action.CrossVPCConfig

    // 1. 查找现有的共享CrossVPC ENI
    enis, err := p.bceClient.ListENIs(ctx, &enisdk.ListEniArgs{
        InstanceId: p.instanceID,
        MaxKeys:    100,
    })
    if err != nil {
        return nil, fmt.Errorf("failed to list ENIs: %w", err)
    }

    // 2. 查找匹配的共享ENI
    for _, eni := range enis {
        if p.isMatchingSharedCrossVPCENI(eni, crossVPCConfig, action.SubnetID) {
            // 检查是否还有可用的IP槽位
            if p.hasAvailableSecondaryIPSlots(eni) {
                log.WithField("existingEniID", eni.ID).Info("found existing shared CrossVPC ENI")
                return eni, nil
            }
        }
    }

    // 3. 没有找到可用的ENI，创建新的共享ENI
    log.Info("no available shared CrossVPC ENI found, creating new one")
    return p.createSharedCrossVPCENI(ctx, action, log)
}

func (p *PrivateCloudBase) isMatchingSharedCrossVPCENI(eni *ENIInfo, crossVPCConfig *DirectIPCrossVPCConfig, subnetID string) bool {
    // 检查ENI标签
    tags := eni.Tags
    if tags["CrossVPC"] != "true" || tags["Mode"] != "secondary" || tags["UserID"] != crossVPCConfig.UserID {
        return false
    }

    // 检查子网ID
    if eni.SubnetID != subnetID {
        return false
    }

    // 检查安全组（至少有一个匹配）
    eniSGSet := make(map[string]bool)
    for _, sg := range eni.SecurityGroupIDs {
        eniSGSet[sg] = true
    }

    for _, sg := range crossVPCConfig.SecurityGroupIDs {
        if eniSGSet[sg] {
            return true // 至少有一个安全组匹配
        }
    }

    return false
}

func (p *PrivateCloudBase) hasAvailableSecondaryIPSlots(eni *ENIInfo) bool {
    // 检查ENI是否还有可用的辅助IP槽位
    maxSecondaryIPs := p.getMaxSecondaryIPsForENI(eni.Type)
    currentSecondaryIPs := len(eni.SecondaryIPs)

    available := currentSecondaryIPs < maxSecondaryIPs
    p.log.WithFields(logrus.Fields{
        "eniID":              eni.ID,
        "currentSecondaryIPs": currentSecondaryIPs,
        "maxSecondaryIPs":    maxSecondaryIPs,
        "available":          available,
    }).Debug("checked secondary IP slots availability")

    return available
}

func (p *PrivateCloudBase) createSharedCrossVPCENI(ctx context.Context, action *DirectIPAction, log *logrus.Entry) (*ENIInfo, error) {
    crossVPCConfig := action.CrossVPCConfig

    // 1. 构造创建参数
    createArgs := &enisdk.CreateEniArgs{
        Name:             p.generateCrossVPCEniName(crossVPCConfig.UserID, "shared"),
        SubnetId:         action.SubnetID,
        InstanceId:       p.instanceID,
        SecurityGroupIds: crossVPCConfig.SecurityGroupIDs,
        Description:      fmt.Sprintf("CrossVPC Shared ENI for UserID: %s", crossVPCConfig.UserID),
        Tags: []enisdk.TagModel{
            {TagKey: "CrossVPC", TagValue: "true"},
            {TagKey: "Mode", TagValue: "secondary"},
            {TagKey: "UserID", TagValue: crossVPCConfig.UserID},
            {TagKey: "CreatedBy", TagValue: "CCE-CNI"},
            {TagKey: "NodeName", TagValue: p.nodeName},
        },
    }

    log.WithField("createArgs", createArgs).Debug("creating shared CrossVPC ENI")

    // 2. 创建ENI
    eniID, err := p.bceClient.CreateENI(ctx, createArgs)
    if err != nil {
        return nil, fmt.Errorf("failed to create shared CrossVPC ENI: %w", err)
    }

    log.WithField("eniID", eniID).Info("shared CrossVPC ENI created, waiting for ready")

    // 3. 等待ENI就绪
    eni, err := p.waitForENIReady(ctx, eniID, 5*time.Minute)
    if err != nil {
        p.cleanupFailedENI(ctx, eniID, log)
        return nil, fmt.Errorf("shared CrossVPC ENI not ready: %w", err)
    }

    // 4. 附加ENI到实例
    err = p.attachENIToInstance(ctx, eniID, log)
    if err != nil {
        p.cleanupFailedENI(ctx, eniID, log)
        return nil, fmt.Errorf("failed to attach shared CrossVPC ENI: %w", err)
    }

    log.WithField("eniID", eniID).Info("shared CrossVPC ENI created and attached successfully")
    return eni, nil
}

func (p *PrivateCloudBase) allocateSecondaryIPForENI(ctx context.Context, eniID, subnetID string, log *logrus.Entry) (string, error) {
    // 1. 为ENI分配辅助IP
    allocateArgs := &enisdk.EniPrivateIpArgs{
        EniId:                    eniID,
        PrivateIpAddressCount:    1, // 分配1个IP
        AllowReassignment:        false,
    }

    log.WithFields(logrus.Fields{
        "eniID":    eniID,
        "subnetID": subnetID,
    }).Debug("allocating secondary IP for ENI")

    result, err := p.bceClient.AddPrivateIp(ctx, allocateArgs)
    if err != nil {
        return "", fmt.Errorf("failed to allocate secondary IP: %w", err)
    }

    if len(result.PrivateIps) == 0 {
        return "", fmt.Errorf("no secondary IP allocated")
    }

    secondaryIP := result.PrivateIps[0]

    // 2. 等待IP分配生效
    err = p.waitForSecondaryIPReady(ctx, eniID, secondaryIP, 2*time.Minute)
    if err != nil {
        // 清理分配失败的IP
        p.cleanupFailedSecondaryIP(ctx, eniID, secondaryIP, log)
        return "", fmt.Errorf("secondary IP not ready: %w", err)
    }

    log.WithFields(logrus.Fields{
        "eniID":       eniID,
        "secondaryIP": secondaryIP,
    }).Info("secondary IP allocated and ready")

    return secondaryIP, nil
}

func (p *PrivateCloudBase) waitForSecondaryIPReady(ctx context.Context, eniID, secondaryIP string, timeout time.Duration) error {
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()

    ticker := time.NewTicker(3 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-timeoutCtx.Done():
            return fmt.Errorf("timeout waiting for secondary IP %s to be ready", secondaryIP)
        case <-ticker.C:
            eni, err := p.bceClient.GetENI(ctx, eniID)
            if err != nil {
                p.log.WithError(err).Debug("failed to get ENI status during secondary IP check")
                continue
            }

            // 检查辅助IP是否已经分配
            for _, ip := range eni.SecondaryIPs {
                if ip == secondaryIP {
                    p.log.WithFields(logrus.Fields{
                        "eniID":       eniID,
                        "secondaryIP": secondaryIP,
                    }).Debug("secondary IP is ready")
                    return nil
                }
            }

            p.log.WithFields(logrus.Fields{
                "eniID":       eniID,
                "secondaryIP": secondaryIP,
            }).Debug("secondary IP not ready yet, continuing to wait")
        }
    }
}

func (p *PrivateCloudBase) cleanupFailedSecondaryIP(ctx context.Context, eniID, secondaryIP string, log *logrus.Entry) {
    log.WithFields(logrus.Fields{
        "eniID":       eniID,
        "secondaryIP": secondaryIP,
    }).Info("cleaning up failed secondary IP")

    err := p.bceClient.DeletePrivateIp(ctx, &enisdk.EniPrivateIpArgs{
        EniId:             eniID,
        PrivateIpAddress:  secondaryIP,
    })
    if err != nil {
        log.WithError(err).Error("failed to cleanup failed secondary IP")
    } else {
        log.Info("failed secondary IP cleaned up successfully")
    }
}
```

### 6.4 辅助方法实现

```go
func (p *PrivateCloudBase) generateCrossVPCEniName(identifier, mode string) string {
    // 生成CrossVPC ENI名称
    timestamp := time.Now().Unix()
    hash := fmt.Sprintf("%x", sha256.Sum256([]byte(identifier)))[:8]
    return fmt.Sprintf("crossvpc-%s-%s-%d", mode, hash, timestamp)
}

func (p *PrivateCloudBase) getSubnetGateway(ctx context.Context, subnetID string) (string, error) {
    subnet, err := p.bceClient.GetSubnet(ctx, subnetID)
    if err != nil {
        return "", fmt.Errorf("failed to get subnet info: %w", err)
    }

    if subnet.Gateway != "" {
        return subnet.Gateway, nil
    }

    // 如果没有明确的网关，计算默认网关（通常是子网的第一个IP）
    return p.calculateDefaultGateway(subnet.CIDR), nil
}

func (p *PrivateCloudBase) calculateDefaultGateway(ipOrCIDR string) string {
    // 简单的网关计算逻辑
    if strings.Contains(ipOrCIDR, "/") {
        // CIDR格式，取第一个可用IP作为网关
        _, ipNet, err := net.ParseCIDR(ipOrCIDR)
        if err != nil {
            return ""
        }
        ip := ipNet.IP.To4()
        if ip != nil {
            ip[3] = ip[3] + 1 // 通常网关是第一个IP
            return ip.String()
        }
    } else {
        // IP格式，计算同网段的网关
        ip := net.ParseIP(ipOrCIDR).To4()
        if ip != nil {
            ip[3] = 1 // 假设网关是.1
            return ip.String()
        }
    }
    return ""
}

func (p *PrivateCloudBase) getMaxSecondaryIPsForENI(eniType string) int {
    // 根据ENI类型返回最大辅助IP数量
    // 这个值应该根据云平台的实际限制来设置
    switch eniType {
    case "standard":
        return 8
    case "enhanced":
        return 16
    default:
        return 8 // 默认值
    }
}
```

### 6.5 释放资源的详细实现

```go
func (p *PrivateCloudBase) releaseCrossVPCPrimaryENI(ctx context.Context, action *DirectIPAction, log *logrus.Entry) error {
    if len(action.Addressing) == 0 {
        log.Warn("no addressing information for release")
        return nil
    }

    eniID := action.Addressing[0].Interface
    if eniID == "" {
        return fmt.Errorf("no ENI ID found in addressing")
    }

    log.WithField("eniID", eniID).Info("releasing CrossVPC primary ENI")

    // 1. 分离ENI
    err := p.bceClient.DetachENI(ctx, &enisdk.DetachEniArgs{
        EniId:      eniID,
        InstanceId: p.instanceID,
    })
    if err != nil {
        log.WithError(err).Error("failed to detach CrossVPC primary ENI")
        return fmt.Errorf("failed to detach CrossVPC primary ENI: %w", err)
    }

    // 2. 等待分离完成
    err = p.waitForENIDetached(ctx, eniID, 2*time.Minute)
    if err != nil {
        log.WithError(err).Error("ENI detach timeout")
        return fmt.Errorf("ENI detach timeout: %w", err)
    }

    // 3. 删除ENI
    err = p.bceClient.DeleteENI(ctx, eniID)
    if err != nil {
        log.WithError(err).Error("failed to delete CrossVPC primary ENI")
        return fmt.Errorf("failed to delete CrossVPC primary ENI: %w", err)
    }

    log.WithField("eniID", eniID).Info("CrossVPC primary ENI released successfully")
    return nil
}

func (p *PrivateCloudBase) releaseCrossVPCSecondaryENI(ctx context.Context, action *DirectIPAction, log *logrus.Entry) error {
    if len(action.Addressing) == 0 {
        log.Warn("no addressing information for release")
        return nil
    }

    eniID := action.Addressing[0].Interface
    secondaryIP := action.Addressing[0].IP

    if eniID == "" || secondaryIP == "" {
        return fmt.Errorf("no ENI ID or secondary IP found in addressing")
    }

    log.WithFields(logrus.Fields{
        "eniID":       eniID,
        "secondaryIP": secondaryIP,
    }).Info("releasing CrossVPC secondary IP")

    // 1. 释放辅助IP
    err := p.bceClient.DeletePrivateIp(ctx, &enisdk.EniPrivateIpArgs{
        EniId:            eniID,
        PrivateIpAddress: secondaryIP,
    })
    if err != nil {
        log.WithError(err).Error("failed to release CrossVPC secondary IP")
        return fmt.Errorf("failed to release CrossVPC secondary IP: %w", err)
    }

    // 2. 检查ENI是否还有其他辅助IP在使用
    eni, err := p.bceClient.GetENI(ctx, eniID)
    if err != nil {
        log.WithError(err).Warn("failed to get ENI info after releasing secondary IP")
        return nil // 不阻塞释放流程
    }

    // 3. 如果ENI没有其他辅助IP在使用，考虑删除整个ENI
    if len(eni.SecondaryIPs) == 0 {
        log.WithField("eniID", eniID).Info("no more secondary IPs, considering ENI cleanup")
        go p.cleanupUnusedSharedENI(context.Background(), eniID) // 异步清理
    }

    log.WithFields(logrus.Fields{
        "eniID":       eniID,
        "secondaryIP": secondaryIP,
    }).Info("CrossVPC secondary IP released successfully")

    return nil
}

func (p *PrivateCloudBase) waitForENIDetached(ctx context.Context, eniID string, timeout time.Duration) error {
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()

    ticker := time.NewTicker(3 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-timeoutCtx.Done():
            return fmt.Errorf("timeout waiting for ENI %s to be detached", eniID)
        case <-ticker.C:
            eni, err := p.bceClient.GetENI(ctx, eniID)
            if err != nil {
                p.log.WithError(err).Debug("failed to get ENI status during detach")
                continue
            }

            if eni.InstanceID == "" && eni.Status == "available" {
                p.log.WithField("eniID", eniID).Info("ENI detached successfully")
                return nil
            }

            p.log.WithFields(logrus.Fields{
                "eniID":      eniID,
                "status":     eni.Status,
                "instanceID": eni.InstanceID,
            }).Debug("ENI not detached yet, continuing to wait")
        }
    }
}

func (p *PrivateCloudBase) cleanupUnusedSharedENI(ctx context.Context, eniID string) {
    log := p.log.WithField("eniID", eniID)

    // 等待一段时间，确保没有新的IP分配请求
    time.Sleep(5 * time.Minute)

    // 再次检查ENI状态
    eni, err := p.bceClient.GetENI(ctx, eniID)
    if err != nil {
        log.WithError(err).Error("failed to get ENI info during cleanup")
        return
    }

    // 如果仍然没有辅助IP，删除ENI
    if len(eni.SecondaryIPs) == 0 {
        log.Info("cleaning up unused shared CrossVPC ENI")

        // 分离ENI
        err = p.bceClient.DetachENI(ctx, &enisdk.DetachEniArgs{
            EniId:      eniID,
            InstanceId: p.instanceID,
        })
        if err != nil {
            log.WithError(err).Error("failed to detach unused shared ENI")
            return
        }

        // 等待分离完成
        err = p.waitForENIDetached(ctx, eniID, 2*time.Minute)
        if err != nil {
            log.WithError(err).Error("failed to wait for unused shared ENI detach")
            return
        }

        // 删除ENI
        err = p.bceClient.DeleteENI(ctx, eniID)
        if err != nil {
            log.WithError(err).Error("failed to delete unused shared ENI")
        } else {
            log.Info("unused shared CrossVPC ENI cleaned up successfully")
        }
    } else {
        log.Info("shared CrossVPC ENI still has secondary IPs, skipping cleanup")
    }
}
```

这个详细的实现展示了CrossVPC ENI申请和IP分配的完整过程，包括：

1. **独占模式**：创建专用ENI → 等待就绪 → 附加到实例 → 返回结果
2. **共享模式**：查找/创建共享ENI → 分配辅助IP → 等待生效 → 返回结果
3. **错误处理**：完善的超时机制、重试逻辑和资源清理
4. **资源管理**：智能的共享ENI复用和自动清理机制

通过这种详细的实现，确保了CrossVPC ENI分配过程的可靠性和效率。
