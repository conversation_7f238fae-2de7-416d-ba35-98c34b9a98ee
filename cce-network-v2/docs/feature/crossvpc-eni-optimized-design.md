# CrossVPCEni 优化设计方案

## 1. 核心优化点

### 1.1 Operator侧优化
- **ENI状态机管理**：创建ENI时记录实例信息到ENI CR，由ENI状态机负责挂载
- **错误码回写**：将云平台错误码回写到CEP，实现故障可运维
- **可重入设计**：支持幂等操作，避免故障导致的资源泄漏
- **异步处理**：使用delay event避免阻塞，提高并发性能
- **节点级IPAM**：每个节点维护用户级的IP池管理
- **删除标记机制**：通过deletionTimestamp标记删除，由ENI状态机处理删除

### 1.2 Agent侧优化
- **类型区分**：独占和共享ENI使用不同的type标识
- **配置差异**：独占ENI无需初始化，共享ENI需要初始化配置
- **MAC地址传递**：独占模式下将MAC地址写入CEP，便于CNI插件定位网卡

### 1.3 IPAM优化
- **启动恢复**：根据当前ENI状态恢复IP池
- **定期同步**：GC机制防止资源泄漏
- **复合Key**：使用(用户,VPC,子网,安全组)作为池标识

## 2. 独占模式流程

### 2.1 独占模式时序图

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant Cloud as Cloud API
    participant Queue as Event Queue

    Pod->>Agent: 创建Pod (mode=primary)
    Agent->>CEP: 创建CEP (type=CrossVPCPrimary)
    
    Note over Operator,Queue: 【可重入处理】
    Operator->>CEP: 检测CEP创建
    Operator->>Operator: 检查是否已有ENI CR
    
    alt ENI CR不存在
        Operator->>Operator: 创建ENI CR (记录instanceID)
        Note right of Operator: ENI CR包含:<br/>- instanceID<br/>- subnetID<br/>- securityGroups
        Operator->>CEP: 更新ENI CR名称到CEP
        Operator->>Queue: 返回delay event (1s后重试)
    else ENI CR存在
        Operator->>Cloud: 查询ENI状态
        alt ENI状态为inuse
            Operator->>CEP: 更新成功状态(包含MAC地址)
        else ENI状态为creating/attaching
            Operator->>Queue: 返回delay event (1s后重试)
        else ENI状态为failed
            Operator->>CEP: 回写错误码
        end
    end
    
    Agent->>CEP: 检测到成功状态
    Agent->>Pod: 返回网络配置 (无需初始化)
```

### 2.2 独占模式伪代码

```go
// Operator处理独占模式
func handleCrossVPCPrimary(cep *CEP) (result, error) {
    // 1. 可重入检查
    if existingENI := findExistingENI(cep.Owner); existingENI != nil {
        status := checkENIStatus(existingENI.ID)
        switch status {
        case "inuse":
            cep.Status = "Ready"
            return Success, nil
        case "creating", "attaching":
            return DelayEvent(1s), nil
        case "failed":
            cep.Status = "Failed"
            cep.ErrorCode = getErrorCode(existingENI.ID)
            return Failed, nil
        }
    }
    
    // 2. 原子化创建ENI
    eniID := cloudAPI.CreateENI(
        instanceID: nodeID,
        subnetID: cep.SubnetID,
        securityGroups: cep.SecurityGroups,
    )
    
    // 3. 记录ENI信息
    cep.Status.ENIID = eniID
    cep.Status.State = "Creating"

    // 4. 返回延迟事件（复用现有事件框架）
    return requeueAfter(1*time.Second), nil
}

// Agent处理独占模式
func configureNetwork(cep *CEP) error {
    // 独占模式：ENI设备直接可用，无需初始化
    return configureDirectENI(cep.ENIID)
}
```

## 3. 共享模式流程

### 3.1 共享模式时序图

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant IPAM as Node IPAM
    participant Cloud as Cloud API
    participant Queue as Event Queue

    Pod->>Agent: 创建Pod (mode=secondary)
    Agent->>CEP: 创建CEP (type=CrossVPCSecondary)
    
    Note over Operator,IPAM: 【节点级IPAM处理】
    Operator->>CEP: 检测CEP创建
    Operator->>IPAM: 请求分配IP (userID, subnetID)
    
    alt IPAM有可用IP
        IPAM-->>Operator: 返回IP地址
        Operator->>CEP: 更新IP信息
    else IPAM无可用IP
        Note over Operator,Cloud: 【可重入ENI创建】
        Operator->>Operator: 检查是否有创建中的ENI
        
        alt 无创建中的ENI
            Operator->>Cloud: 原子化创建共享ENI
            Cloud-->>Operator: 返回ENI ID
            Operator->>IPAM: 注册新ENI到IP池
            Operator->>Queue: 返回delay event (1s后重试)
        else 有创建中的ENI
            Operator->>Cloud: 查询ENI状态
            alt ENI就绪
                Operator->>IPAM: 将ENI加入IP池
                Operator->>IPAM: 分配IP
                IPAM-->>Operator: 返回IP地址
                Operator->>CEP: 更新IP信息
            else ENI创建中
                Operator->>Queue: 返回delay event (1s后重试)
            else ENI失败
                Operator->>CEP: 回写错误码
                Operator->>IPAM: 清理失败的ENI记录
            end
        end
    end
    
    Agent->>CEP: 检测到IP分配成功
    Agent->>Agent: 初始化共享ENI网络配置
    Agent->>Pod: 返回网络配置
```

### 3.2 共享模式伪代码

```go
// 节点级IPAM
type NodeIPAM struct {
    pools map[PoolKey]*IPPool  // 复合Key -> IP池
}

type PoolKey struct {
    UserID         string
    VPCID          string
    SubnetID       string
    SecurityGroups string  // 排序后的安全组ID列表
}

type IPPool struct {
    availableIPs []string
    enis         map[string]*ENIInfo
    maxIPsPerENI int
}

// Operator处理共享模式
func handleCrossVPCSecondary(cep *CEP) (result, error) {
    nodeIPAM := getNodeIPAM(cep.NodeName)
    
    // 1. 尝试从IPAM分配IP
    if ip := nodeIPAM.AllocateIP(cep.UserID, cep.SubnetID); ip != "" {
        cep.Status.IP = ip
        cep.Status.State = "Ready"
        return Success, nil
    }
    
    // 2. 可重入检查是否有创建中的ENI
    if creatingENI := nodeIPAM.GetCreatingENI(cep.UserID, cep.SubnetID); creatingENI != nil {
        status := checkENIStatus(creatingENI.ID)
        switch status {
        case "inuse":
            nodeIPAM.AddENIToPool(creatingENI)
            return DelayEvent(100ms), nil  // 快速重试分配IP
        case "creating":
            return DelayEvent(1s), nil
        case "failed":
            nodeIPAM.CleanupFailedENI(creatingENI.ID)
            cep.Status = "Failed"
            cep.ErrorCode = getErrorCode(creatingENI.ID)
            return Failed, nil
        }
    }
    
    // 3. 创建新的共享ENI
    eniID := cloudAPI.CreateENI(
        instanceID: nodeID,
        subnetID: cep.SubnetID,
        securityGroups: cep.SecurityGroups,
        maxSecondaryIPs: 8,
    )
    
    nodeIPAM.RegisterCreatingENI(cep.UserID, cep.SubnetID, eniID)
    return DelayEvent(1s), nil
}

// Agent处理共享模式
func configureNetwork(cep *CEP) error {
    // 共享模式：需要初始化网络配置
    return configureSecondaryIP(cep.IP, cep.ENIID)
}

// IPAM失败释放机制
func (pool *UserIPPool) ReleaseIP(ip string) {
    pool.availableIPs = append(pool.availableIPs, ip)
    
    // 检查ENI是否完全空闲
    if eni := pool.findENIByIP(ip); eni != nil && eni.isFullyIdle() {
        // 异步清理空闲ENI
        go pool.cleanupIdleENI(eni.ID)
    }
}
```

## 4. IPAM恢复和同步机制

### 4.1 IPAM启动恢复流程

```mermaid
sequenceDiagram
    participant Operator as Operator启动
    participant IPAM as Node IPAM
    participant Cloud as Cloud API
    participant CEP as CEP Store

    Note over Operator,CEP: 【启动恢复阶段】
    Operator->>IPAM: 初始化IPAM
    IPAM->>Cloud: 查询节点所有ENI
    Cloud-->>IPAM: 返回ENI列表

    loop 遍历每个ENI
        IPAM->>IPAM: 解析ENI标签获取PoolKey
        IPAM->>Cloud: 查询ENI的辅助IP列表
        Cloud-->>IPAM: 返回IP列表
        IPAM->>CEP: 查询使用该ENI的CEP
        CEP-->>IPAM: 返回CEP列表
        IPAM->>IPAM: 计算可用IP = 总IP - 已使用IP
        IPAM->>IPAM: 将可用IP加入对应池
    end

    Note over Operator,CEP: 【定期GC阶段】
    loop 每5分钟
        IPAM->>CEP: 查询所有CrossVPC类型CEP
        IPAM->>IPAM: 对比IPAM记录与CEP实际使用
        IPAM->>IPAM: 回收泄漏的IP到可用池
        IPAM->>Cloud: 清理完全空闲的ENI
    end
```

### 4.2 IPAM恢复伪代码

```go
// IPAM启动恢复
func (ipam *NodeIPAM) RecoverFromENIs() error {
    // 1. 查询节点所有ENI
    enis := cloudAPI.ListENIs(nodeID)

    for _, eni := range enis {
        // 2. 只处理CrossVPC类型的ENI
        if !isCrossVPCENI(eni) {
            continue
        }

        // 3. 解析PoolKey
        poolKey := parsePoolKeyFromENI(eni)
        if poolKey == nil {
            continue
        }

        // 4. 获取或创建IP池
        pool := ipam.getOrCreatePool(poolKey)

        // 5. 恢复ENI的IP信息
        pool.recoverENI(eni)
    }

    return nil
}

func (pool *IPPool) recoverENI(eni *ENIInfo) {
    // 1. 查询ENI的所有辅助IP
    allIPs := cloudAPI.GetENISecondaryIPs(eni.ID)

    // 2. 查询正在使用该ENI的CEP
    usedIPs := getUsedIPsFromCEPs(eni.ID)

    // 3. 计算可用IP
    availableIPs := difference(allIPs, usedIPs)

    // 4. 更新IP池
    pool.enis[eni.ID] = eni
    pool.availableIPs = append(pool.availableIPs, availableIPs...)
}

// 解析ENI标签获取PoolKey
func parsePoolKeyFromENI(eni *ENIInfo) *PoolKey {
    tags := eni.Tags
    if tags["CrossVPC"] != "true" || tags["Mode"] != "secondary" {
        return nil
    }

    return &PoolKey{
        UserID:         tags["UserID"],
        VPCID:          tags["VPCID"],
        SubnetID:       eni.SubnetID,
        SecurityGroups: tags["SecurityGroups"], // 已排序的安全组列表
    }
}
```

### 4.3 定期GC机制

```go
// 定期GC，防止泄漏
func (ipam *NodeIPAM) StartGC() {
    ticker := time.NewTicker(5 * time.Minute)
    go func() {
        for range ticker.C {
            ipam.performGC()
        }
    }()
}

func (ipam *NodeIPAM) performGC() {
    // 1. 查询所有CrossVPC类型的CEP
    ceps := k8sClient.ListCEPs(CrossVPCTypes)

    // 2. 构建实际使用的IP映射
    actualUsedIPs := make(map[string]string) // IP -> CEPID
    for _, cep := range ceps {
        if cep.Status.IP != "" {
            actualUsedIPs[cep.Status.IP] = cep.Name
        }
    }

    // 3. 检查每个IP池
    for poolKey, pool := range ipam.pools {
        pool.performGC(actualUsedIPs)
    }
}

func (pool *IPPool) performGC(actualUsedIPs map[string]string) {
    for eniID, eni := range pool.enis {
        // 1. 查询ENI当前的辅助IP
        currentIPs := cloudAPI.GetENISecondaryIPs(eniID)

        // 2. 找出泄漏的IP（ENI有但CEP中没有使用的）
        for _, ip := range currentIPs {
            if _, used := actualUsedIPs[ip]; !used {
                // 泄漏的IP，回收到可用池
                pool.availableIPs = append(pool.availableIPs, ip)
                log.Warnf("recovered leaked IP %s from ENI %s", ip, eniID)
            }
        }

        // 3. 检查ENI是否完全空闲
        if len(currentIPs) == 0 {
            // 异步清理空闲ENI
            go pool.cleanupIdleENI(eniID)
        }
    }
}
```

## 5. 错误处理优化

### 4.1 错误码回写机制

```mermaid
flowchart TD
    A[云平台API调用] --> B{调用结果}
    B -->|成功| C[更新CEP状态为Ready]
    B -->|失败| D[解析错误码]
    
    D --> E{错误类型}
    E -->|QuotaExceeded| F[CEP.ErrorCode = QUOTA_EXCEEDED]
    E -->|InvalidSubnet| G[CEP.ErrorCode = INVALID_SUBNET]
    E -->|InsufficientCapacity| H[CEP.ErrorCode = INSUFFICIENT_CAPACITY]
    E -->|ThrottlingException| I[返回DelayEvent重试]
    
    F --> J[CEP.Status = Failed]
    G --> J
    H --> K[返回DelayEvent重试]
    
    J --> L[Agent检测失败状态]
    L --> M[返回具体错误给Pod]
```

### 4.2 错误处理伪代码

```go
func handleCloudError(err error, cep *CEP) (result, error) {
    switch err.Code {
    case "QuotaExceeded":
        cep.Status.State = "Failed"
        cep.Status.ErrorCode = "QUOTA_EXCEEDED"
        cep.Status.ErrorMessage = "ENI quota exceeded"
        return Failed, nil
        
    case "InvalidSubnet":
        cep.Status.State = "Failed"
        cep.Status.ErrorCode = "INVALID_SUBNET"
        cep.Status.ErrorMessage = "Invalid subnet configuration"
        return Failed, nil
        
    case "InsufficientCapacity":
        cep.Status.ErrorCode = "INSUFFICIENT_CAPACITY"
        return DelayEvent(5s), nil  // 容量不足，延长重试间隔
        
    case "ThrottlingException":
        return DelayEvent(2s), nil  // API限流，短暂重试
        
    default:
        if isRetryable(err.Code) {
            return DelayEvent(1s), nil
        } else {
            cep.Status.State = "Failed"
            cep.Status.ErrorCode = err.Code
            cep.Status.ErrorMessage = err.Message
            return Failed, nil
        }
    }
}
```

## 5. 事件处理优化

### 5.1 复用现有事件框架

```mermaid
stateDiagram-v2
    [*] --> Processing: CEP创建/更新
    Processing --> Requeue: 需要延迟重试
    Requeue --> Processing: 延迟后重新处理
    Processing --> Success: 处理成功
    Processing --> Failed: 处理失败
    Success --> [*]
    Failed --> [*]

    note right of Requeue
        延迟策略：
        - 正常重试: 1s
        - API限流: 2s
        - 容量不足: 5s
    end note
```

### 5.2 事件处理伪代码

```go
// 复用现有的事件处理框架，不做修改
func (c *CrossVPCController) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    cep := &ccev2.CCEEndpoint{}
    if err := c.Get(ctx, req.NamespacedName, cep); err != nil {
        return ctrl.Result{}, client.IgnoreNotFound(err)
    }

    // 处理CrossVPC ENI分配
    result, err := c.handleCrossVPCAllocation(ctx, cep)

    switch result.Type {
    case "Success":
        return ctrl.Result{}, nil

    case "Failed":
        return ctrl.Result{}, nil  // 不重试，等待用户干预

    case "Requeue":
        return ctrl.Result{
            RequeueAfter: result.Duration,  // 使用框架的RequeueAfter
        }, nil

    default:
        return ctrl.Result{}, err
    }
}

// 处理结果类型
type ProcessResult struct {
    Type     string        // "Success", "Failed", "Requeue"
    Duration time.Duration // 重新入队延迟时间
}

func (c *CrossVPCController) handlePrimaryMode(ctx context.Context, cep *ccev2.CCEEndpoint) (ProcessResult, error) {
    // 可重入检查
    if existingENI := c.findExistingENI(cep); existingENI != nil {
        status := c.checkENIStatus(existingENI.ID)
        switch status {
        case "inuse":
            // 更新CEP状态，包含MAC地址
            cep.Status.ENIID = existingENI.ID
            cep.Status.MACAddress = existingENI.MACAddress  // 新增MAC地址
            cep.Status.State = "Ready"
            c.Update(ctx, cep)
            return ProcessResult{Type: "Success"}, nil

        case "creating", "attaching":
            return ProcessResult{Type: "Requeue", Duration: 1 * time.Second}, nil

        case "failed":
            cep.Status.State = "Failed"
            cep.Status.ErrorCode = c.getErrorCode(existingENI.ID)
            c.Update(ctx, cep)
            return ProcessResult{Type: "Failed"}, nil
        }
    }

    // 原子化创建ENI
    eniID, err := c.cloudAPI.CreateENI(ctx, &CreateENIRequest{
        InstanceID:     c.nodeID,  // 直接指定实例ID，实现原子化
        SubnetID:       cep.Spec.Network.IPAllocation.CrossVPCConfig.SubnetID,
        SecurityGroups: cep.Spec.Network.IPAllocation.CrossVPCConfig.SecurityGroupIDs,
    })

    if err != nil {
        return c.handleCloudError(err, cep)
    }

    // 更新CEP状态
    cep.Status.ENIID = eniID
    cep.Status.State = "Creating"
    c.Update(ctx, cep)

    // 延迟重试检查状态
    return ProcessResult{Type: "Requeue", Duration: 1 * time.Second}, nil
}
```

## 6. 核心优势总结

### 6.1 可靠性提升
- **原子化操作**：创建ENI时直接指定实例ID，创建+挂载一步完成
- **可重入设计**：支持故障恢复，避免资源泄漏和重复创建
- **错误可观测**：错误码回写到CEP，便于运维排查和自动化处理
- **IPAM恢复**：启动时自动恢复IP池状态，防止资源丢失

### 6.2 性能优化
- **异步处理**：使用现有事件框架的RequeueAfter，避免阻塞
- **智能重试**：根据错误类型调整重试策略和延迟时间
- **资源复用**：节点级IPAM提高共享ENI利用率
- **复合Key**：精确的池标识避免资源冲突

### 6.3 运维友好
- **故障自愈**：可重入机制支持自动恢复，无需人工干预
- **错误透明**：具体错误码便于问题定位和自动化处理
- **资源清理**：定期GC机制自动清理泄漏资源
- **MAC地址传递**：独占模式下传递MAC地址，便于CNI插件定位网卡

### 6.4 关键改进点

| 改进点 | 优化前 | 优化后 | 收益 |
|--------|--------|--------|------|
| **ENI创建** | 创建后再挂载 | 原子化创建+挂载 | 减少中间状态，提高成功率 |
| **错误处理** | 通用错误信息 | 具体错误码回写 | 便于自动化处理和运维 |
| **故障恢复** | 手动清理 | 可重入+自动恢复 | 提高系统自愈能力 |
| **事件处理** | 修改框架 | 复用现有框架 | 降低改造风险，保持一致性 |
| **IPAM管理** | 简单Key | 复合Key+恢复机制 | 精确管理，防止泄漏 |

这个优化设计通过原子化操作、可重入处理、IPAM恢复机制和复用现有事件框架，显著提升了CrossVPC ENI的可靠性、性能和运维友好性，同时最小化了对现有系统的改动。
