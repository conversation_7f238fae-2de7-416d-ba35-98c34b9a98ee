# CrossVPCEni 基于PSTS和GC机制的实现方案

## 1. 设计理念

### 1.1 参考现有机制
- **共享模式**：参考非固定IP模式的PSTS分配机制
- **ENI延迟删除**：参考固定IP模式的CEP GC机制
- **复用现有框架**：最大化利用现有的IPAM和GC基础设施

### 1.2 核心优势
- **实现简化**：复用成熟的PSTS分配和GC逻辑
- **稳定可靠**：基于已验证的生产环境代码
- **维护成本低**：减少新增代码量，降低维护复杂度

## 2. 共享模式：基于非reuse IP的PSTS分配

### 2.1 非reuse IP模式的PSTS分配机制

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant Cloud as Cloud API

    Note over Pod,Cloud: 【共享模式 - 参考非reuse IP的PSTS分配】

    Pod->>Agent: 创建Pod (mode=secondary)
    Agent->>CEP: 创建CEP (type=CrossVPCSecondary)

    Operator->>CEP: 检测CEP创建
    Note over Operator: 跳过本地池分配<br/>(非reuse IP模式)

    Operator->>Cloud: 直接向子网申请新IP
    Note right of Cloud: AllocatePrivateIP(subnetID, userID)
    Cloud-->>Operator: 返回IP信息
    Operator->>CEP: 更新IP信息

    Agent->>CEP: 检测到Ready状态
    Agent->>Pod: 配置网络(辅助IP模式)
```

### 2.2 参考非reuse IP的PSTS，结合CrossVPC特点的实现

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant Cloud as Cloud API

    Note over Pod,Cloud: 【参考PSTS，结合CrossVPC特点】

    Pod->>Agent: 创建Pod (mode=secondary)
    Agent->>CEP: 创建CEP (type=CrossVPCSecondary)
    Note right of CEP: 包含CrossVPC配置：<br/>- userID<br/>- subnetID<br/>- securityGroups

    Operator->>CEP: 检测CEP创建
    Note over Operator: 参考PSTS的DirectIPAction<br/>但结合CrossVPC特点

    Operator->>Operator: 构造CrossVPC DirectIPAction
    Note right of Operator: action.CrossVPCConfig = {<br/>  userID: config.UserID,<br/>  mode: "secondary",<br/>  subnetID: config.SubnetID<br/>}

    Operator->>Cloud: 调用DirectEndpointOperation
    Note right of Cloud: AllocateCrossVPCIP(<br/>  subnetID, userID, mode<br/>)

    alt 分配成功
        Cloud-->>Operator: 返回IP信息
        Operator->>CEP: 更新addressing
        Note right of CEP: addressing[0] = {<br/>  ip: "*************",<br/>  subnet: subnetID,<br/>  family: IPv4<br/>}
    else 分配失败
        Cloud-->>Operator: 返回错误
        Operator->>CEP: 更新错误状态
        Note right of CEP: state: "Failed"<br/>errorCode: "QUOTA_EXCEEDED"
    end

    Agent->>CEP: 检测到Ready状态
    Agent->>Pod: 配置网络(辅助IP模式)
```

### 2.3 CrossVPC与PSTS的关键差异

| 方面 | PSTS非reuse IP模式 | CrossVPC共享模式 | 适配要点 |
|------|------------------|-----------------|---------|
| **分配目标** | 节点子网IP | 跨VPC子网IP | 需要指定目标子网 |
| **用户隔离** | 无特殊要求 | 按userID隔离 | 需要传递userID |
| **安全组** | 节点安全组 | 用户指定安全组 | 需要传递securityGroups |
| **网络配置** | 节点网络 | 跨VPC网络 | 需要特殊路由配置 |
| **资源标记** | 节点标记 | CrossVPC标记 | 需要特殊标签 |

## 3. ENI延迟删除：基于ENI状态机的GC机制

### 3.1 ENI状态机内置GC机制

```mermaid
sequenceDiagram
    participant ENISM as ENI State Machine
    participant ENIC as ENI CR
    participant CEP as CEP Store
    participant Cloud as Cloud API
    participant ExpireMap as 过期映射

    Note over ENISM,ExpireMap: 【ENI状态机内置GC流程】

    loop 定时GC检查 (每2分钟)
        ENISM->>ENIC: 扫描所有共享模式ENI CR

        loop 遍历每个ENI CR
            ENISM->>ENIC: 检查ENI CR状态
            Note right of ENIC: 检查：<br/>- secondaryIPCount<br/>- 创建时间<br/>- 标签信息

            alt ENI CR显示有辅助IP
                ENISM->>ExpireMap: 从过期映射移除
                Note right of ExpireMap: 该ENI仍在使用
            else ENI CR显示无辅助IP
                ENISM->>CEP: 查询使用该ENI的CEP

                alt 有CEP使用该ENI
                    ENISM->>ExpireMap: 从过期映射移除
                    Note right of ExpireMap: CEP还在使用，不删除
                else 无CEP使用该ENI
                    ENISM->>ExpireMap: 检查是否在过期映射中

                    alt 不在过期映射中
                        ENISM->>ExpireMap: 加入过期映射
                        Note right of ExpireMap: 记录首次发现时间
                    else 已在过期映射中
                        ENISM->>ExpireMap: 检查过期时间

                        alt 未超过延迟时间
                            ENISM->>ExpireMap: 更新最后检查时间
                        else 超过延迟时间
                            ENISM->>Cloud: 查询ENI真实状态
                            Note right of Cloud: 最后确认：<br/>GetENI(eniID)

                            alt 云平台确认无辅助IP
                                ENISM->>Cloud: 分离ENI
                                ENISM->>Cloud: 删除ENI
                                ENISM->>ExpireMap: 从过期映射移除
                                ENISM->>ENIC: 移除finalizer
                            else 云平台显示有辅助IP
                                ENISM->>ExpireMap: 从过期映射移除
                                Note right of ExpireMap: 状态不一致，重新开始
                            end
                        end
                    end
                end
            end
        end
    end
```

### 3.2 ENI状态机GC的详细流程

```mermaid
sequenceDiagram
    participant Timer as GC定时器
    participant ENISM as ENI State Machine
    participant ENIC as ENI CR
    participant CEP as CEP Store
    participant Cloud as Cloud API
    participant ExpireMap as 过期映射

    Note over Timer,ExpireMap: 【ENI状态机内置GC详细流程】

    Timer->>ENISM: 触发GC检查 (每2分钟)
    ENISM->>ENIC: 列出所有共享模式ENI CR
    Note right of ENIC: 过滤条件：<br/>useMode = CrossVPCSecondary

    loop 遍历每个共享ENI CR
        ENISM->>ENIC: 读取ENI CR状态
        Note right of ENIC: 检查字段：<br/>- status.secondaryIPCount<br/>- metadata.creationTimestamp<br/>- spec.crossVPCConfig

        alt ENI CR显示有辅助IP (secondaryIPCount > 0)
            ENISM->>ExpireMap: 从过期映射移除该ENI
            Note right of ExpireMap: 该ENI仍在使用中

        else ENI CR显示无辅助IP (secondaryIPCount = 0)
            ENISM->>CEP: 查询使用该ENI的CEP
            Note right of CEP: 查询条件：<br/>- type = CrossVPCSecondary<br/>- status.eniCRName = eniCR.name<br/>- addressing.interface = eniID

            alt 找到CEP使用该ENI
                ENISM->>ExpireMap: 从过期映射移除该ENI
                Note right of ExpireMap: CEP还在使用，状态可能未同步

            else 无CEP使用该ENI
                ENISM->>ExpireMap: 检查过期映射

                alt ENI不在过期映射中
                    ENISM->>ExpireMap: 加入过期映射
                    Note right of ExpireMap: 记录：<br/>- eniCRName<br/>- firstSeenTime: now<br/>- userID, subnetID

                else ENI已在过期映射中
                    ENISM->>ExpireMap: 检查过期时间
                    Note right of ExpireMap: 计算：<br/>now - firstSeenTime > delayDuration

                    alt 未超过延迟时间
                        ENISM->>ExpireMap: 更新lastCheckedTime
                        Note right of ExpireMap: 继续等待

                    else 超过延迟时间
                        Note over ENISM,Cloud: 【最终确认阶段】
                        ENISM->>Cloud: 查询ENI真实状态
                        Note right of Cloud: GetENI(eniID)<br/>获取最新的辅助IP列表

                        alt 云平台确认无辅助IP
                            Note over ENISM,Cloud: 【执行删除】
                            ENISM->>Cloud: 分离ENI (如果已挂载)
                            ENISM->>Cloud: 删除ENI
                            ENISM->>ExpireMap: 从过期映射移除
                            ENISM->>ENIC: 移除finalizer
                            Note right of ENIC: ENI CR被删除

                        else 云平台显示有辅助IP
                            ENISM->>ExpireMap: 从过期映射移除
                            Note right of ExpireMap: 状态不一致，重新开始计时
                            ENISM->>ENIC: 更新CR状态 (如需要)
                        end
                    end
                end
            end
        end
    end

    Note over Timer,ExpireMap: 【清理过期映射】
    ENISM->>ExpireMap: 清理不存在的ENI CR条目
```

### 3.3 ENI状态机GC的关键优势

```mermaid
flowchart TD
    A[ENI状态机GC] --> B[统一状态管理]
    A --> C[最终确认机制]
    A --> D[避免竞态条件]

    B --> B1[ENI CR状态]
    B --> B2[CEP使用状态]
    B --> B3[过期映射状态]

    C --> C1[CR状态检查]
    C --> C2[CEP使用检查]
    C --> C3[云平台最终确认]

    D --> D1[基于K8s资源状态]
    D --> D2[延迟删除机制]
    D --> D3[状态机统一处理]

    style A fill:#e3f2fd
    style C fill:#fff3e0
    style D fill:#e8f5e8
```

## 4. 配置集成

### 4.1 配置扩展

```go
// pkg/option/config.go
type DaemonConfig struct {
    // 现有配置...
    
    // CrossVPC ENI配置
    CrossVPCENIEnabled              bool          `mapstructure:"crossvpc-eni-enabled"`
    CrossVPCENISharedDeletionDelay  time.Duration `mapstructure:"crossvpc-eni-shared-deletion-delay"`
    CrossVPCENIGCInterval           time.Duration `mapstructure:"crossvpc-eni-gc-interval"`
}

// 默认配置
var (
    DefaultCrossVPCENISharedDeletionDelay = 5 * time.Minute
    DefaultCrossVPCENIGCInterval          = 2 * time.Minute
)
```

### 4.2 ConfigMap配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  # CrossVPC ENI配置
  crossvpc-eni-enabled: "true"
  crossvpc-eni-shared-deletion-delay: "300s"    # 共享ENI延迟删除时间
  crossvpc-eni-gc-interval: "120s"              # GC检查间隔
```

## 4. 整体流程对比

### 4.1 传统GC vs ENI状态机GC

| 方面 | 传统GC方式 | ENI状态机GC | 优势 |
|------|-----------|------------|------|
| **执行位置** | Operator独立GC | ENI状态机内置 | 统一状态管理 |
| **状态检查** | 云平台API查询 | K8s CR状态 + 最终确认 | 减少API调用，提高准确性 |
| **删除执行** | 直接调用云平台API | 状态机统一处理 | 保证状态一致性 |
| **竞态处理** | 难以避免 | 基于K8s资源状态 | 更好的并发安全 |

### 4.2 PSTS参考 vs 直接照搬

| 方面 | 直接照搬PSTS | 参考PSTS适配 | CrossVPC特点 |
|------|-------------|-------------|-------------|
| **IP分配** | 使用本地池 | 直接分配新IP | 每次新IP，用户隔离 |
| **子网选择** | 节点子网 | 用户指定子网 | 跨VPC子网 |
| **安全组** | 节点安全组 | 用户指定安全组 | 用户自定义安全策略 |
| **资源标记** | 节点标记 | CrossVPC标记 | 便于识别和管理 |

## 5. 优势总结

### 5.1 ENI状态机GC的核心优势
- **统一状态管理**：所有ENI操作都在状态机中处理，状态一致性更好
- **最终确认机制**：删除前查询云平台真实状态，避免误删
- **避免竞态条件**：基于K8s资源状态，减少并发冲突
- **简化架构**：不需要独立的GC组件，减少系统复杂度

### 5.2 参考PSTS的适配优势
- **成熟框架**：基于已验证的PSTS分配框架
- **灵活适配**：结合CrossVPC特点进行必要调整
- **减少重复**：复用DirectIPAction等核心结构
- **保持一致**：与现有IPAM框架保持架构一致性

### 5.3 整体方案优势
- **安全可靠**：多层检查机制，避免IP泄漏
- **性能优化**：减少不必要的云平台API调用
- **维护简单**：复用现有机制，降低维护成本
- **扩展性好**：基于成熟框架，便于后续扩展

这个优化后的方案通过将GC集成到ENI状态机中，并参考而非照搬PSTS机制，实现了更加安全、可靠和高效的CrossVPC ENI管理。
