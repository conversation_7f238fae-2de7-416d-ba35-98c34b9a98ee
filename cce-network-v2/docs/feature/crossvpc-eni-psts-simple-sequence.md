# CrossVPCEni 基于PSTS模式的简洁流程图

## 1. 整体流程时序图

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent IPAM
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant Cloud as Cloud API
    participant Node as Node

    Note over Pod,Node: 【简洁流程】Agent写入 → Operator处理 → Agent返回
    
    Pod->>Agent: Pod创建，带CrossVPC注解
    Agent->>Agent: 检测CrossVPC请求
    Agent->>Agent: 解析CrossVPC配置
    
    Note over Agent,CEP: 【Agent写入阶段】
    Agent->>CEP: 创建CCEEndpoint
    Agent->>CEP: 写入CrossVPC配置到IPAllocation
    Agent->>Agent: 等待Operator处理
    
    Note over Operator,Cloud: 【Operator处理阶段】
    Operator->>CEP: 监听CEP创建事件
    Operator->>Operator: 识别CrossVPC类型
    Operator->>Operator: 根据mode选择处理逻辑
    
    alt 独占模式 (CrossVPCPrimary)
        Operator->>Cloud: 创建专用CrossVPC ENI
        Cloud-->>Operator: 返回ENI信息
        Operator->>CEP: 回写ENI信息到addressing
    else 共享模式 (CrossVPCSecondary)
        Operator->>Cloud: 查找/创建共享CrossVPC ENI
        Operator->>Cloud: 分配辅助IP
        Cloud-->>Operator: 返回IP信息
        Operator->>CEP: 回写IP信息到addressing
    end
    
    Note over Agent,Pod: 【Agent返回阶段】
    Agent->>CEP: 检测到CEP状态更新
    Agent->>Agent: 解析分配结果
    Agent->>Node: 配置CrossVPC网络
    Agent->>Pod: 返回网络配置
```

## 2. 独占模式详细流程

```mermaid
sequenceDiagram
    participant Pod as Pod (Primary Mode)
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Allocator
    participant Cloud as Cloud API

    Pod->>Agent: 创建Pod (mode=primary)
    Agent->>Agent: 解析注解：userID, subnetID, privateIP等
    
    Agent->>CEP: 创建CCEEndpoint
    Note right of CEP: IPAllocation:<br/>type: CrossVPCPrimary<br/>crossVPCConfig: {...}
    
    Operator->>CEP: 监听到CEP创建
    Operator->>Operator: 识别为CrossVPCPrimary类型
    
    Operator->>Cloud: CreateCrossVPCENI(独占模式)
    Note right of Cloud: 参数：<br/>- userID<br/>- subnetID<br/>- securityGroupIDs<br/>- privateIPAddress
    
    Cloud-->>Operator: 返回ENI信息
    Note left of Cloud: 返回：<br/>- eniID<br/>- primaryIP<br/>- macAddress
    
    Operator->>CEP: 更新addressing字段
    Note right of CEP: addressing:<br/>- ip: primaryIP<br/>- interface: eniID<br/>- family: 4
    
    Agent->>CEP: 检测到状态更新
    Agent->>Agent: 解析ENI信息
    Agent->>Pod: 返回网络配置
```

## 3. 共享模式详细流程

```mermaid
sequenceDiagram
    participant Pod1 as Pod1 (Secondary)
    participant Pod2 as Pod2 (Secondary)
    participant Agent as Agent
    participant CEP1 as CEP1
    participant CEP2 as CEP2
    participant Operator as CrossVPC Allocator
    participant Cloud as Cloud API

    Note over Pod1,Cloud: 【第一个Pod - 创建共享ENI】
    Pod1->>Agent: 创建Pod1 (mode=secondary)
    Agent->>CEP1: 创建CEP1 (CrossVPCSecondary)
    
    Operator->>CEP1: 监听到CEP1创建
    Operator->>Cloud: 查找共享CrossVPC ENI
    Cloud-->>Operator: 未找到
    Operator->>Cloud: 创建共享CrossVPC ENI
    Operator->>Cloud: 分配辅助IP给Pod1
    Cloud-->>Operator: 返回IP1信息
    Operator->>CEP1: 更新addressing (IP1)
    
    Agent->>CEP1: 检测状态更新
    Agent->>Pod1: 返回网络配置
    
    Note over Pod2,Cloud: 【第二个Pod - 复用共享ENI】
    Pod2->>Agent: 创建Pod2 (mode=secondary)
    Agent->>CEP2: 创建CEP2 (CrossVPCSecondary)
    
    Operator->>CEP2: 监听到CEP2创建
    Operator->>Cloud: 查找共享CrossVPC ENI
    Cloud-->>Operator: 找到现有ENI
    Operator->>Cloud: 为现有ENI分配新的辅助IP
    Cloud-->>Operator: 返回IP2信息
    Operator->>CEP2: 更新addressing (IP2)
    
    Agent->>CEP2: 检测状态更新
    Agent->>Pod2: 返回网络配置
```

## 4. 关键数据结构流转

### 4.1 Pod注解 → CCEEndpoint

```yaml
# Pod注解
annotations:
  cross-vpc-eni.cce.io/enabled: "true"
  cross-vpc-eni.cce.io/mode: "primary"
  cross-vpc-eni.cce.io/userID: "user123"
  cross-vpc-eni.cce.io/subnetID: "sbn-abc123"

# 转换为CCEEndpoint
spec:
  network:
    ipAllocation:
      type: "CrossVPCPrimary"
      crossVPCConfig:
        userID: "user123"
        subnetID: "sbn-abc123"
        mode: "primary"
```

### 4.2 Operator处理 → 回写结果

```yaml
# Operator处理前
status:
  networking:
    addressing: []  # 空

# Operator处理后
status:
  networking:
    addressing:
    - family: 4
      ip: "***********00"
      subnet: "sbn-abc123"
      interface: "eni-crossvpc-123"
      gateway: "***********"
```

## 5. 错误处理流程

```mermaid
flowchart TD
    A[Agent创建CEP] --> B{Operator处理}
    B -->|成功| C[更新CEP状态]
    B -->|失败| D[记录错误到CEP]
    
    C --> E[Agent检测成功]
    D --> F[Agent检测失败]
    
    E --> G[配置网络]
    F --> H[返回错误给Pod]
    
    G --> I[Pod网络就绪]
    H --> J[Pod创建失败]
    
    style D fill:#ffcdd2
    style F fill:#ffcdd2
    style H fill:#ffcdd2
    style J fill:#ffcdd2
```

## 6. 状态机转换

```mermaid
stateDiagram-v2
    [*] --> Pending: Agent创建CEP
    Pending --> Processing: Operator开始处理
    Processing --> IPAllocated: 分配成功
    Processing --> Failed: 分配失败
    IPAllocated --> Ready: Agent配置网络完成
    Failed --> [*]: 清理资源
    Ready --> Deleting: Pod删除
    Deleting --> [*]: 资源回收完成
    
    note right of Processing
        Operator根据mode:
        - primary: 创建独占ENI
        - secondary: 分配共享IP
    end note
```

## 7. 核心优势

### 7.1 流程简洁
1. **三步流程**：Agent写入 → Operator处理 → Agent返回
2. **异步处理**：Operator异步处理，不阻塞Pod创建
3. **状态驱动**：基于CCEEndpoint状态变化驱动流程

### 7.2 架构复用
1. **复用PSTS模式**：90%复用现有的CCEEndpoint + Operator架构
2. **复用EndpointManager**：复用现有的endpoint管理框架
3. **复用云平台接口**：复用现有的DirectIPAllocator接口

### 7.3 双模式支持
1. **独占模式**：高性能，Pod独占整个CrossVPC ENI
2. **共享模式**：成本优化，多个Pod共享CrossVPC ENI的不同IP
3. **灵活配置**：通过Pod注解灵活选择模式和参数

这个基于PSTS模式的简洁实现方案，通过清晰的三步流程和完善的状态管理，实现了高效的CrossVPCEni分配和管理。
