# CrossVPCEni 删除流程详细设计

## 1. 删除流程概述

### 1.1 删除策略对比

| 模式 | 删除策略 | 触发时机 | 处理方式 | 延迟时间 |
|------|---------|---------|---------|---------|
| **独占模式** | 立即删除 | Pod删除 | ENI状态机处理 | 0秒 |
| **共享模式** | 延迟删除 | 最后一个IP释放 | Operator调度删除 | 可配置(默认300秒) |

### 1.2 删除流程状态机

```mermaid
stateDiagram-v2
    [*] --> PodRunning: Pod创建
    PodRunning --> PodDeleting: Pod删除事件
    
    state PodDeleting {
        [*] --> CheckMode
        CheckMode --> PrimaryMode: 独占模式
        CheckMode --> SecondaryMode: 共享模式
        
        state PrimaryMode {
            [*] --> MarkENIForDeletion
            MarkENIForDeletion --> ENIDetaching
            ENIDetaching --> ENIDeleting
            ENIDeleting --> ENIDeleted
        }
        
        state SecondaryMode {
            [*] --> ReleaseIP
            ReleaseIP --> CheckLastIP
            CheckLastIP --> ScheduleDeletion: 最后一个IP
            CheckLastIP --> IPReleased: 还有其他IP
            ScheduleDeletion --> WaitDelay
            WaitDelay --> CheckNewAllocation
            CheckNewAllocation --> CancelDeletion: 有新分配
            CheckNewAllocation --> DeleteENI: 无新分配
            DeleteENI --> ENIDeleted
        }
    }
    
    PrimaryMode --> [*]
    SecondaryMode --> [*]
```

## 2. 独占模式删除详细流程

### 2.1 独占模式删除时序图

```mermaid
sequenceDiagram
    participant K8s as Kubernetes
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over K8s,Cloud: 【独占模式删除流程】
    
    K8s->>Agent: Pod删除事件
    Agent->>Agent: 检测到Pod删除
    Agent->>CEP: 标记CEP删除 (deletionTimestamp)
    
    Operator->>CEP: 监听CEP删除事件
    Operator->>Operator: 识别为独占模式
    Operator->>ENIC: 标记ENI CR删除
    Note right of ENIC: 添加deletionTimestamp<br/>保留finalizer
    
    ENISM->>ENIC: 监听ENI CR删除事件
    ENISM->>ENISM: 检查删除策略 (immediate)
    
    Note over ENISM,Cloud: 【ENI分离阶段】
    ENISM->>Cloud: 分离ENI
    Note right of Cloud: DetachENI(eniID, instanceID)
    Cloud-->>ENISM: 分离请求已提交
    ENISM->>ENIC: 更新状态 (Detaching)
    
    loop 等待分离完成
        ENISM->>Cloud: 查询ENI状态
        Cloud-->>ENISM: 返回ENI状态
        alt ENI已分离
            ENISM->>ENIC: 更新状态 (Detached)
        else ENI分离中
            ENISM->>ENISM: 等待1秒后重试
        end
    end
    
    Note over ENISM,Cloud: 【ENI删除阶段】
    ENISM->>Cloud: 删除ENI
    Note right of Cloud: DeleteENI(eniID)
    Cloud-->>ENISM: 删除成功
    ENISM->>ENIC: 移除finalizer
    
    ENIC->>ENIC: ENI CR被删除
    Operator->>CEP: 移除CEP finalizer
    CEP->>CEP: CEP被删除
```

### 2.2 独占模式错误处理

```mermaid
flowchart TD
    A[开始删除ENI] --> B{分离ENI}
    B -->|成功| C[等待分离完成]
    B -->|失败| D{检查错误类型}
    
    D -->|ENI不存在| E[直接删除CR]
    D -->|权限错误| F[记录错误，重试]
    D -->|其他错误| G[指数退避重试]
    
    C --> H{分离完成?}
    H -->|是| I[删除ENI]
    H -->|否| J[继续等待]
    
    I --> K{删除成功?}
    K -->|是| L[移除finalizer]
    K -->|否| M{检查错误}
    
    M -->|ENI不存在| L
    M -->|其他错误| N[重试删除]
    
    J --> C
    F --> B
    G --> B
    N --> I
    L --> O[删除完成]
    E --> O
    
    style D fill:#ffebee
    style F fill:#ffcdd2
    style G fill:#ffcdd2
    style M fill:#ffebee
    style N fill:#ffcdd2
```

## 3. 共享模式删除详细流程

### 3.1 共享模式删除时序图

```mermaid
sequenceDiagram
    participant K8s as Kubernetes
    participant Agent as Agent
    participant CEP1 as CEP1
    participant CEP2 as CEP2
    participant Operator as CrossVPC Operator
    participant Cloud as Cloud API
    participant Queue as Deletion Queue

    Note over K8s,Queue: 【共享模式删除流程】
    
    Note over K8s,CEP1: 【第一个Pod删除】
    K8s->>Agent: Pod1删除事件
    Agent->>CEP1: 标记CEP1删除
    
    Operator->>CEP1: 监听CEP1删除事件
    Operator->>Cloud: 释放IP1
    Note right of Cloud: ReleasePrivateIP(ip1)
    Cloud-->>Operator: IP1释放成功
    
    Operator->>Operator: 检查是否为最后一个IP
    Note right of Operator: 查询同ENI的其他CEP<br/>发现还有CEP2使用
    Operator->>CEP1: 清理CEP1 (无需删除ENI)
    
    Note over K8s,CEP2: 【最后一个Pod删除】
    K8s->>Agent: Pod2删除事件
    Agent->>CEP2: 标记CEP2删除
    
    Operator->>CEP2: 监听CEP2删除事件
    Operator->>Cloud: 释放IP2
    Cloud-->>Operator: IP2释放成功
    
    Operator->>Operator: 检查是否为最后一个IP
    Note right of Operator: 查询同ENI的其他CEP<br/>发现没有其他CEP
    
    Note over Operator,Queue: 【启动延迟删除】
    Operator->>Queue: 创建延迟删除任务
    Note right of Queue: 任务信息：<br/>- userID<br/>- subnetID<br/>- scheduledAt: now + 300s
    
    Operator->>CEP2: 清理CEP2
    
    Note over Queue,Cloud: 【延迟删除处理】
    loop 每30秒检查
        Queue->>Queue: 检查到期任务
        Queue->>Operator: 处理到期任务
        
        Operator->>Operator: 再次检查是否有新的IP分配
        alt 有新的IP分配
            Operator->>Queue: 取消删除任务
        else 无新分配且到期
            Operator->>Cloud: 删除共享ENI
            Cloud-->>Operator: 删除成功
            Operator->>Queue: 移除删除任务
        end
    end
```

### 3.2 延迟删除队列管理

```mermaid
flowchart TD
    A[最后一个IP释放] --> B[创建删除任务]
    B --> C[添加到延迟队列]
    
    C --> D[定时检查队列]
    D --> E{任务到期?}
    E -->|否| D
    E -->|是| F[检查新分配]
    
    F --> G{有新IP分配?}
    G -->|是| H[取消删除任务]
    G -->|否| I[执行ENI删除]
    
    I --> J{删除成功?}
    J -->|是| K[移除任务]
    J -->|否| L[重新调度任务]
    
    H --> M[任务结束]
    K --> M
    L --> D
    
    style B fill:#e3f2fd
    style C fill:#e3f2fd
    style I fill:#fff3e0
    style H fill:#e8f5e8
    style L fill:#ffebee
```

## 4. 配置和监控

### 4.1 删除配置

```yaml
# ConfigMap配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  # 删除策略配置
  crossvpc-eni-primary-deletion-delay: "0"        # 独占模式立即删除
  crossvpc-eni-shared-deletion-delay: "300"       # 共享模式延迟5分钟
  crossvpc-eni-deletion-check-interval: "30"      # 删除队列检查间隔30秒
  crossvpc-eni-deletion-retry-delay: "60"         # 删除失败重试间隔60秒
  
  # 超时配置
  crossvpc-eni-detach-timeout: "120"              # ENI分离超时2分钟
  crossvpc-eni-delete-timeout: "60"               # ENI删除超时1分钟
```

### 4.2 删除监控指标

```go
// 删除相关监控指标
var (
    crossVPCEniDeletionTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cce_crossvpc_eni_deletion_total",
            Help: "Total number of CrossVPC ENI deletions",
        },
        []string{"node", "mode", "result"}, // result: success/failed
    )
    
    crossVPCEniDeletionDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "cce_crossvpc_eni_deletion_duration_seconds",
            Help: "Duration of CrossVPC ENI deletion",
            Buckets: []float64{1, 5, 10, 30, 60, 120, 300},
        },
        []string{"node", "mode"},
    )
    
    crossVPCEniDeletionQueueSize = prometheus.NewGauge(
        prometheus.GaugeOpts{
            Name: "cce_crossvpc_eni_deletion_queue_size",
            Help: "Number of ENIs in deletion queue",
        },
    )
    
    crossVPCEniDeletionDelayed = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cce_crossvpc_eni_deletion_delayed_total",
            Help: "Total number of delayed ENI deletions",
        },
        []string{"node", "reason"}, // reason: scheduled/cancelled
    )
)
```

### 4.3 删除告警规则

```yaml
# Prometheus告警规则
groups:
- name: crossvpc-eni-deletion
  rules:
  # 删除失败率告警
  - alert: CrossVPCEniDeletionFailureRateHigh
    expr: |
      (
        rate(cce_crossvpc_eni_deletion_total{result="failed"}[5m]) /
        rate(cce_crossvpc_eni_deletion_total[5m])
      ) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "CrossVPC ENI deletion failure rate is high"
      description: "CrossVPC ENI deletion failure rate is {{ $value | humanizePercentage }} on node {{ $labels.node }}"
      
  # 删除队列积压告警
  - alert: CrossVPCEniDeletionQueueBacklog
    expr: cce_crossvpc_eni_deletion_queue_size > 10
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CrossVPC ENI deletion queue has backlog"
      description: "CrossVPC ENI deletion queue size is {{ $value }}, indicating potential issues"
      
  # 删除延迟过长告警
  - alert: CrossVPCEniDeletionLatencyHigh
    expr: |
      histogram_quantile(0.95, 
        rate(cce_crossvpc_eni_deletion_duration_seconds_bucket[5m])
      ) > 300
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CrossVPC ENI deletion latency is high"
      description: "95th percentile of CrossVPC ENI deletion latency is {{ $value }}s on node {{ $labels.node }}"
```

## 5. 故障排查

### 5.1 删除故障诊断流程

```bash
# 1. 检查删除中的ENI CR
kubectl get enis -l cce.baidubce.com/eni-type=crossvpc --field-selector metadata.deletionTimestamp!=

# 2. 检查ENI CR的删除状态
kubectl describe eni <eni-name>

# 3. 检查删除队列状态
curl -s http://localhost:9090/metrics | grep crossvpc_eni_deletion_queue_size

# 4. 检查删除失败的ENI
kubectl get events --field-selector reason=ENIDeletionFailed

# 5. 检查ENI状态机日志
kubectl logs -n kube-system -l app=cce-network-v2-operator --tail=100 | grep -i "eni.*delet"
```

### 5.2 常见删除问题

| 问题 | 症状 | 原因 | 解决方案 |
|------|------|------|---------|
| **ENI分离失败** | ENI CR卡在Detaching状态 | 实例状态异常或权限问题 | 检查实例状态，验证权限配置 |
| **ENI删除失败** | ENI CR有finalizer但无法删除 | 云平台API错误或ENI被其他资源占用 | 检查云平台状态，手动清理依赖 |
| **删除队列积压** | 队列大小持续增长 | 删除处理逻辑异常或云平台限流 | 检查处理逻辑，调整重试策略 |
| **延迟删除不生效** | 共享ENI未按时删除 | 配置错误或有新的IP分配 | 检查配置，确认IP分配状态 |

这个详细的删除流程设计提供了完整的CrossVPC ENI删除机制，包括立即删除和延迟删除两种策略，确保资源的正确清理和成本优化。
