# CrossVPCEni 共享模式解决方案

## 1. 方案概述

共享模式下，多个Pod共享ENI设备，通过辅助IP的方式为Pod分配网络。CrossVPC配置从Pod的annotation中获取，CNI配置得到简化。

### 1.1 特点

- **资源效率**：多个Pod共享ENI，节省ENI资源
- **配置灵活**：通过Pod annotation动态配置
- **网络实现**：使用veth pair + 辅助IP
- **删除策略**：延迟删除，避免频繁创建销毁

### 1.2 架构图

```mermaid
flowchart TD
    subgraph "Pod1 Network Namespace"
        Pod1[Pod1]
        Veth1[veth1]
    end
    
    subgraph "Pod2 Network Namespace"
        Pod2[Pod2]
        Veth2[veth2]
    end
    
    subgraph "Host Network"
        ENI[Shared ENI Device]
        CNI[CrossVPC CNI Plugin]
        Agent[CCE Network Agent]
        Veth1Host[veth1-host]
        Veth2Host[veth2-host]
    end
    
    subgraph "Kubernetes Control Plane"
        CEP1[CCEEndpoint1]
        CEP2[CCEEndpoint2]
        ENIC[ENI CR]
        Operator[CrossVPC Operator]
        ENISM[ENI State Machine]
    end
    
    subgraph "Cloud Platform"
        Cloud[BCE Cloud API]
    end
    
    Pod1 --> Veth1
    Pod2 --> Veth2
    Veth1 -.-> Veth1Host
    Veth2 -.-> Veth2Host
    Veth1Host --> ENI
    Veth2Host --> ENI
    
    CNI --> Agent
    Agent --> CEP1
    Agent --> CEP2
    Operator --> CEP1
    Operator --> CEP2
    Operator --> ENIC
    ENISM --> ENIC
    ENISM --> Cloud
    
    style ENI fill:#c8e6c9
    style CNI fill:#e3f2fd
    style Agent fill:#fff3e0
    style Operator fill:#e8f5e8
```

## 2. Pod Annotation配置

### 2.1 支持的注解

| 注解名 | 描述 | 必需 | 示例 |
|--------|------|------|------|
| `cross-vpc-eni.cce.io/userID` | 用户ID | 是 | `user123` |
| `cross-vpc-eni.cce.io/subnetID` | 子网ID | 是 | `sbn-abc123` |
| `cross-vpc-eni.cce.io/securityGroupIDs` | 安全组ID列表（逗号分隔） | 是 | `sg-123456,sg-789012` |
| `cross-vpc-eni.cce.io/privateIPAddress` | 私有IP地址 | 否 | `*************` |
| `cross-vpc-eni.cce.io/vpcCidr` | VPC CIDR | 否 | `***********/16` |
| `cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation` | 默认路由接口委托 | 否 | `true` |
| `cross-vpc-eni.cce.io/defaultRouteExcludedCidrs` | 排除的CIDR列表（逗号分隔） | 否 | `10.0.0.0/8,**********/12` |

### 2.2 Pod示例

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  annotations:
    cce.io/network-type: "crossvpc-secondary"
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456,sg-789012"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "true"
    cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "10.0.0.0/8"
spec:
  containers:
  - name: test
    image: nginx
```

## 3. CNI插件实现

### 3.1 简化的CNI配置

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-network",
  "type": "crossvpc-secondary",
  "ipam": {
    "type": "cce-network-agent"
  }
}
```

### 3.2 CNI ADD流程

```mermaid
sequenceDiagram
    participant Runtime as Container Runtime
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant Host as Host Network

    Note over Runtime,Host: 【共享模式 CNI ADD流程】

    Runtime->>CNI: CNI ADD调用
    Note right of CNI: 参数：<br/>- netns: /proc/123/ns/net<br/>- ifname: eth0<br/>- config: 简化配置

    CNI->>CNI: 解析CNI配置
    Note right of CNI: 解析：<br/>- type: crossvpc-secondary<br/>- 提取Pod信息(namespace/name)

    CNI->>Agent: 请求IP分配
    Note right of Agent: IPAM请求：<br/>POST /ipam/allocate<br/>body: containerID, netns, podName

    Agent->>CNI: 返回网络配置
    Note right of CNI: 响应：<br/>- ip: *************<br/>- gateway: ***********<br/>- 无需interface和mac

    CNI->>CNI: 创建veth pair
    Note right of CNI: ip link add veth0 type veth<br/>peer name veth0-host

    CNI->>CNI: 配置辅助IP和路由
    Note right of CNI: - 移动veth0到容器netns<br/>- 配置IP地址和路由<br/>- 配置host端路由

    CNI->>Runtime: 返回成功结果
```

### 3.3 CNI DEL流程

```mermaid
sequenceDiagram
    participant Runtime as Container Runtime
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent

    Note over Runtime,Agent: 【共享模式 CNI DEL流程】
    
    Runtime->>CNI: CNI DEL调用
    CNI->>CNI: 解析容器ID和网络配置
    
    CNI->>CNI: 删除veth pair
    Note right of CNI: 清理容器网络配置
    
    CNI->>Agent: 请求IP释放
    Note right of Agent: IPAM请求：<br/>POST /ipam/release<br/>body: 容器标识
    
    Agent->>CNI: 返回释放结果
    CNI->>Runtime: 返回成功结果
```

## 4. Agent实现

### 4.1 Agent ADD流程（详细）

```mermaid
sequenceDiagram
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant K8s as Kubernetes API
    participant CEP as CCEEndpoint
    participant Pod as Pod Resource

    Note over CNI,Pod: 【共享模式 Agent ADD流程】

    CNI->>Agent: POST /ipam/allocate
    Note right of Agent: 请求体：<br/>- containerID<br/>- netns<br/>- owner: namespace/podName

    Agent->>Agent: 解析owner获取Pod信息
    Note right of Agent: 从owner中提取<br/>namespace和podName

    Agent->>K8s: 获取Pod对象
    Note right of K8s: GET /api/v1/namespaces/{ns}/pods/{name}
    K8s-->>Agent: 返回Pod对象

    Agent->>Agent: 解析Pod annotation
    Note right of Agent: 解析crossvpc相关annotation<br/>构造CrossVPCConfig

    Agent->>Agent: 检查现有CEP
    Note right of Agent: 根据containerID查找<br/>是否已有CEP存在

    alt 无现有CEP
        Agent->>CEP: 创建CCEEndpoint
        Note right of CEP: spec:<br/>- type: CrossVPCSecondary<br/>- crossVPCConfig: 从annotation解析<br/>- nodeName<br/>- containerID, netns

        Agent->>K8s: 提交CEP到K8s
        K8s-->>Agent: CEP创建成功

    else 有现有CEP
        Agent->>Agent: 复用现有CEP
    end

    loop 等待IP分配 (最多5分钟)
        Agent->>K8s: 查询CEP状态
        K8s-->>Agent: 返回CEP状态

        alt 分配成功
            Agent->>CNI: 返回网络配置
            Note right of CNI: 响应：<br/>- ip, gateway<br/>- 无interface和mac
        else 分配失败
            Agent->>CNI: 返回错误信息
        else 分配中
            Agent->>Agent: 等待2秒后重试
        end
    end
```

### 4.2 Agent实现要点

Agent的实现参考EndpointAllocator中使用PSTS但不reuse IP的方式：

1. **检测CrossVPC请求**：通过Pod annotation检测是否为CrossVPC请求
2. **解析配置**：从Pod annotation中解析CrossVPC相关配置
3. **CEP创建**：参考非reuse IP的PSTS实现，创建CrossVPCSecondary类型的CEP
4. **等待分配**：等待Operator完成IP分配并更新CEP状态
5. **返回结果**：返回分配的IP信息给CNI插件

**关键流程**：
- 使用`createDelegateEndpoint`方法创建CEP
- 对于非reuse IP模式，删除旧的CEP并创建新的
- 通过`waitEndpointIPAllocated`等待IP分配完成
- 设置适当的ReleaseStrategy为TTL

## 5. Operator实现

### 5.1 Operator ADD流程（参考非reuse IP的PSTS）

```mermaid
sequenceDiagram
    participant CEP as CCEEndpoint
    participant Manager as EndpointManager
    participant Operation as DirectEndpointOperation
    participant Cloud as Cloud API

    Note over CEP,Cloud: 【共享模式ADD流程】

    CEP->>Manager: CEP创建/更新事件
    Note right of CEP: type: CrossVPCSecondary

    Manager->>Manager: 检查CEP状态
    Note right of Manager: 检查status.networking.addressing<br/>是否已有IP分配

    alt 无现有IP分配
        Manager->>Manager: 构造DirectIPAction
        Note right of Manager: action:<br/>- NodeName<br/>- Owner<br/>- SubnetID<br/>- CrossVPCConfig

        Manager->>Operation: 调用AllocateIP
        Note right of Operation: operation.AllocateIP(ctx, action)

        Operation->>Cloud: 创建/查找共享ENI
        Note right of Cloud: 根据subnetID和securityGroups<br/>查找或创建ENI

        Operation->>Cloud: 分配辅助IP
        Note right of Cloud: AssignPrivateIpAddresses<br/>(eniID, 1)
        Cloud-->>Operation: 返回分配的IP

        Operation-->>Manager: 更新action.Addressing

        Manager->>CEP: 更新CEP状态
        Note right of CEP: status.networking.addressing<br/>包含分配的IP信息

    else 已有IP分配
        Manager->>Manager: 复用现有IP
        Note right of Manager: 使用现有的addressing信息
    end
```

### 5.2 Operator实现要点

Operator的实现参考EndpointManager中非reuse IP的PSTS实现：

1. **DirectIPAction构造**：构造包含CrossVPC配置的DirectIPAction
2. **DirectEndpointOperation调用**：通过operation.AllocateIP执行实际的IP分配
3. **云API调用**：在AllocateIP方法中直接调用云API创建ENI和分配IP
4. **状态更新**：将分配结果更新到CEP的status.networking.addressing
5. **错误处理**：支持重试和错误状态管理

**关键流程**：
- 检查CEP状态，如果已有addressing则复用
- 构造DirectIPAction，包含NodeName、Owner、SubnetID等信息
- 调用operation.AllocateIP执行分配
- 更新CEP状态为Ready并设置IP信息

## 6. ENI状态机GC流程（修改版）

```mermaid
sequenceDiagram
    participant Timer as GC定时器
    participant ENISM as ENI State Machine
    participant ENIC as ENI CR
    participant Cloud as Cloud API

    Note over Timer,Cloud: 【ENI状态机GC流程 - 修改版】
    
    Timer->>ENISM: 触发GC (每2分钟)
    ENISM->>ENIC: 扫描共享模式ENI CR
    
    loop 遍历每个ENI CR
        ENISM->>ENIC: 检查secondaryIPCount
        
        alt 有辅助IP
            ENISM->>ENISM: 跳过删除
            Note right of ENISM: 仍有Pod在使用
        else 无辅助IP
            ENISM->>ENISM: 检查过期时间
            
            alt 超过延迟时间
                Note over ENISM,Cloud: 直接标记删除，不再检查CEP
                ENISM->>ENIC: 标记ENI为待删除
                Note right of ENIC: 添加删除标记<br/>准备删除流程
                
                ENISM->>Cloud: 分离ENI
                Note right of Cloud: DetachNetworkInterface
                ENISM->>Cloud: 删除ENI
                Note right of Cloud: DeleteNetworkInterface
                ENISM->>ENIC: 移除finalizer
                
            else 未超过延迟时间
                ENISM->>ENISM: 继续等待
                Note right of ENISM: 延迟删除计时中
            end
        end
    end
```

## 7. 配置示例

### 7.1 Operator配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  crossvpc-eni-enabled: "true"
  crossvpc-eni-shared-mode-enabled: "true"
  crossvpc-eni-shared-deletion-delay: "300s"
  crossvpc-eni-gc-interval: "120s"
```

## 8. CNI配置实例

### 8.1 作为第一CNI（主网络接口）

当crossvpc-secondary作为第一CNI时，它负责为Pod提供主网络接口：

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-network",
  "plugins": [
    {
      "type": "crossvpc-secondary",
      "ipam": {
        "type": "cce-network-agent"
      }
    }
  ]
}
```

**Pod配置示例**：
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: web-app
  annotations:
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-web-tier"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-web,sg-common"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
spec:
  containers:
  - name: nginx
    image: nginx:1.20
    ports:
    - containerPort: 80
```

**网络结果**：
- Pod获得跨VPC的IP地址（如*************）
- eth0接口连接到跨VPC网络
- 默认路由通过跨VPC网关

### 8.2 作为第二CNI（多网络接口）

当crossvpc-secondary作为第二CNI时，它为Pod提供额外的网络接口：

```json
{
  "cniVersion": "0.4.0",
  "name": "k8s-pod-network",
  "plugins": [
    {
      "type": "cptp",
      "ipam": {
        "type": "cipam"
      }
    },
    {
      "type": "crossvpc-secondary",
      "ipam": {
        "type": "cce-network-agent"
      }
    }
  ]
}
```

**Pod配置示例**：
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: database-app
  annotations:
    # 主网络使用默认配置
    # 跨VPC网络配置
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-db-tier"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-database,sg-common"
    cross-vpc-eni.cce.io/vpcCidr: "10.0.0.0/8"
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "false"
    cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "10.0.0.0/8"
spec:
  containers:
  - name: mysql
    image: mysql:8.0
    ports:
    - containerPort: 3306
```

**网络结果**：
- eth0：主网络接口（集群内通信）
- eth1：跨VPC网络接口（跨VPC通信）
- 默认路由仍通过eth0
- 特定路由（10.0.0.0/8）通过eth1

### 8.3 高级配置示例

**指定IP地址**：
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: fixed-ip-app
  annotations:
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456"
    cross-vpc-eni.cce.io/privateIPAddress: "*************"  # 指定IP
spec:
  containers:
  - name: app
    image: myapp:latest
```

**默认路由委托**：
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: gateway-app
  annotations:
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456"
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "true"  # 委托默认路由
    cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "10.0.0.0/8,**********/12"  # 排除内网
spec:
  containers:
  - name: gateway
    image: gateway:latest
```

共享模式通过Pod annotation配置和ENI复用，提供了更高的资源利用率和配置灵活性，适用于大规模Pod部署场景。支持作为第一CNI或第二CNI使用，满足不同的网络需求。
