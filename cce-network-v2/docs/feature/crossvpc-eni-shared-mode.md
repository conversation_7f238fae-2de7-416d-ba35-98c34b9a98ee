# CrossVPCEni 共享模式解决方案

## 1. 方案概述

共享模式下，多个Pod共享ENI设备，通过辅助IP的方式为Pod分配网络。CrossVPC配置从Pod的annotation中获取，CNI配置得到简化。

### 1.1 特点

- **资源效率**：多个Pod共享ENI，节省ENI资源
- **配置灵活**：通过Pod annotation动态配置
- **网络实现**：使用veth pair + 辅助IP
- **删除策略**：延迟删除，避免频繁创建销毁

### 1.2 架构图

```mermaid
flowchart TD
    subgraph "Pod1 Network Namespace"
        Pod1[Pod1]
        Veth1[veth1]
    end
    
    subgraph "Pod2 Network Namespace"
        Pod2[Pod2]
        Veth2[veth2]
    end
    
    subgraph "Host Network"
        ENI[Shared ENI Device]
        CNI[CrossVPC CNI Plugin]
        Agent[CCE Network Agent]
        Veth1Host[veth1-host]
        Veth2Host[veth2-host]
    end
    
    subgraph "Kubernetes Control Plane"
        CEP1[CCEEndpoint1]
        CEP2[CCEEndpoint2]
        ENIC[ENI CR]
        Operator[CrossVPC Operator]
        ENISM[ENI State Machine]
    end
    
    subgraph "Cloud Platform"
        Cloud[BCE Cloud API]
    end
    
    Pod1 --> Veth1
    Pod2 --> Veth2
    Veth1 -.-> Veth1Host
    Veth2 -.-> Veth2Host
    Veth1Host --> ENI
    Veth2Host --> ENI
    
    CNI --> Agent
    Agent --> CEP1
    Agent --> CEP2
    Operator --> CEP1
    Operator --> CEP2
    Operator --> ENIC
    ENISM --> ENIC
    ENISM --> Cloud
    
    style ENI fill:#c8e6c9
    style CNI fill:#e3f2fd
    style Agent fill:#fff3e0
    style Operator fill:#e8f5e8
```

## 2. Pod Annotation配置

### 2.1 支持的注解

| 注解名 | 描述 | 必需 | 示例 |
|--------|------|------|------|
| `cross-vpc-eni.cce.io/userID` | 用户ID | 是 | `user123` |
| `cross-vpc-eni.cce.io/subnetID` | 子网ID | 是 | `sbn-abc123` |
| `cross-vpc-eni.cce.io/securityGroupIDs` | 安全组ID列表（逗号分隔） | 是 | `sg-123456,sg-789012` |
| `cross-vpc-eni.cce.io/privateIPAddress` | 私有IP地址 | 否 | `*************` |
| `cross-vpc-eni.cce.io/vpcCidr` | VPC CIDR | 否 | `***********/16` |
| `cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation` | 默认路由接口委托 | 否 | `true` |
| `cross-vpc-eni.cce.io/defaultRouteExcludedCidrs` | 排除的CIDR列表（逗号分隔） | 否 | `10.0.0.0/8,**********/12` |

### 2.2 Pod示例

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  annotations:
    cce.io/network-type: "crossvpc-secondary"
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456,sg-789012"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "true"
    cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "10.0.0.0/8"
spec:
  containers:
  - name: test
    image: nginx
```

## 3. CNI插件实现

### 3.1 简化的CNI配置

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-network",
  "type": "crossvpc-secondary",
  "ipam": {
    "type": "cce-network-agent"
  }
}
```

### 3.2 CNI ADD流程

```mermaid
sequenceDiagram
    participant Runtime as Container Runtime
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant Host as Host Network

    Note over Runtime,Host: 【共享模式 CNI ADD流程】
    
    Runtime->>CNI: CNI ADD调用
    Note right of CNI: 参数：<br/>- netns: /proc/123/ns/net<br/>- ifname: eth0<br/>- config: 简化配置
    
    CNI->>CNI: 解析CNI配置
    Note right of CNI: 解析：<br/>- type: crossvpc-secondary<br/>- 从Pod annotation获取crossvpc配置
    
    CNI->>Agent: 请求IP分配
    Note right of Agent: IPAM请求：<br/>POST /ipam/allocate<br/>body: Pod信息 + annotation配置
    
    Agent->>CNI: 返回网络配置
    Note right of CNI: 响应：<br/>- ip: *************<br/>- gateway: ***********<br/>- 无需interface和mac
    
    CNI->>CNI: 创建veth pair
    Note right of CNI: ip link add veth0 type veth<br/>peer name veth0-host
    
    CNI->>CNI: 配置辅助IP和路由
    Note right of CNI: - 移动veth0到容器netns<br/>- 配置IP地址和路由<br/>- 配置host端路由
    
    CNI->>Runtime: 返回成功结果
```

### 3.3 CNI DEL流程

```mermaid
sequenceDiagram
    participant Runtime as Container Runtime
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent

    Note over Runtime,Agent: 【共享模式 CNI DEL流程】
    
    Runtime->>CNI: CNI DEL调用
    CNI->>CNI: 解析容器ID和网络配置
    
    CNI->>CNI: 删除veth pair
    Note right of CNI: 清理容器网络配置
    
    CNI->>Agent: 请求IP释放
    Note right of Agent: IPAM请求：<br/>POST /ipam/release<br/>body: 容器标识
    
    Agent->>CNI: 返回释放结果
    CNI->>Runtime: 返回成功结果
```

## 4. Agent实现

Agent的实现与独占模式类似，主要区别在于：

1. **配置获取**：从Pod annotation中获取crossvpc配置
2. **CEP类型**：创建`CrossVPCSecondary`类型的CEP
3. **返回信息**：不需要返回interface和mac信息

## 5. Operator实现

### 5.1 共享模式ADD流程（细化）

```mermaid
sequenceDiagram
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over CEP,Cloud: 【共享模式ADD流程 - 第一个用户Pod】
    
    CEP->>Operator: CEP创建事件
    Note right of CEP: type: CrossVPCSecondary
    
    Operator->>Operator: 查找现有共享ENI CR
    Note right of Operator: 根据subnetID和securityGroups<br/>查找可复用的ENI CR
    
    alt 无可用ENI CR
        Note over Operator,Cloud: 创建新的共享ENI
        Operator->>ENIC: 创建ENI CR
        Note right of ENIC: spec:<br/>- useMode: CrossVPCSecondary<br/>- instanceID: node-123<br/>- crossVPCConfig<br/>- deletionPolicy: delayed
        
        ENISM->>ENIC: 监听ENI CR创建
        ENISM->>Cloud: 创建ENI
        Note right of Cloud: CreateNetworkInterface<br/>(subnetID, securityGroups)
        Cloud-->>ENISM: 返回ENI ID
        
        ENISM->>Cloud: 挂载ENI到实例
        Note right of Cloud: AttachNetworkInterface<br/>(eniID, instanceID)
        Cloud-->>ENISM: 挂载完成
        
        ENISM->>Cloud: 分配辅助IP
        Note right of Cloud: AssignPrivateIpAddresses<br/>(eniID, 1)
        Cloud-->>ENISM: 返回分配的IP
        
        ENISM->>ENIC: 更新ENI状态
        Note right of ENIC: status:<br/>- eniID: eni-shared123<br/>- secondaryIPs: [*************]<br/>- state: Ready
        
        Operator->>CEP: 更新CEP状态
        Note right of CEP: status:<br/>- ip: *************<br/>- eniID: eni-shared123<br/>- state: Ready
        
    else 有可用ENI CR
        Note over Operator,Cloud: 复用现有ENI
        Operator->>ENISM: 请求分配辅助IP
        ENISM->>Cloud: 分配辅助IP
        Cloud-->>ENISM: 返回分配的IP
        ENISM->>ENIC: 更新ENI状态
        Operator->>CEP: 更新CEP状态
    end
```

### 5.2 共享模式ADD流程（后续Pod）

```mermaid
sequenceDiagram
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over CEP,Cloud: 【共享模式ADD流程 - 后续用户Pod】
    
    CEP->>Operator: CEP创建事件
    Note right of CEP: type: CrossVPCSecondary
    
    Operator->>Operator: 查找现有共享ENI CR
    Note right of Operator: 根据subnetID和securityGroups<br/>查找可复用的ENI CR
    
    Operator->>ENISM: 请求分配辅助IP
    Note right of ENISM: 在现有ENI上分配新的辅助IP
    
    ENISM->>Cloud: 分配辅助IP
    Note right of Cloud: AssignPrivateIpAddresses<br/>(eniID, 1)
    Cloud-->>ENISM: 返回分配的IP
    
    ENISM->>ENIC: 更新ENI状态
    Note right of ENIC: status:<br/>- secondaryIPs: [..., *************]<br/>- secondaryIPCount: 2
    
    Operator->>CEP: 更新CEP状态
    Note right of CEP: status:<br/>- ip: *************<br/>- eniID: eni-shared123<br/>- state: Ready
```

## 6. ENI状态机GC流程（修改版）

```mermaid
sequenceDiagram
    participant Timer as GC定时器
    participant ENISM as ENI State Machine
    participant ENIC as ENI CR
    participant Cloud as Cloud API

    Note over Timer,Cloud: 【ENI状态机GC流程 - 修改版】
    
    Timer->>ENISM: 触发GC (每2分钟)
    ENISM->>ENIC: 扫描共享模式ENI CR
    
    loop 遍历每个ENI CR
        ENISM->>ENIC: 检查secondaryIPCount
        
        alt 有辅助IP
            ENISM->>ENISM: 跳过删除
            Note right of ENISM: 仍有Pod在使用
        else 无辅助IP
            ENISM->>ENISM: 检查过期时间
            
            alt 超过延迟时间
                Note over ENISM,Cloud: 直接标记删除，不再检查CEP
                ENISM->>ENIC: 标记ENI为待删除
                Note right of ENIC: 添加删除标记<br/>准备删除流程
                
                ENISM->>Cloud: 分离ENI
                Note right of Cloud: DetachNetworkInterface
                ENISM->>Cloud: 删除ENI
                Note right of Cloud: DeleteNetworkInterface
                ENISM->>ENIC: 移除finalizer
                
            else 未超过延迟时间
                ENISM->>ENISM: 继续等待
                Note right of ENISM: 延迟删除计时中
            end
        end
    end
```

## 7. 配置示例

### 7.1 Operator配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  crossvpc-eni-enabled: "true"
  crossvpc-eni-shared-mode-enabled: "true"
  crossvpc-eni-shared-deletion-delay: "300s"
  crossvpc-eni-gc-interval: "120s"
```

共享模式通过Pod annotation配置和ENI复用，提供了更高的资源利用率和配置灵活性，适用于大规模Pod部署场景。
