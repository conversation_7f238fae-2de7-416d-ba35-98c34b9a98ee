# CrossVPCEni 基于PSTS模式的简洁实现方案

## 1. 方案概述

参照PSTS的分配方式实现CrossVPCEni特性，支持独占和共享两种模式，通过Agent-Operator协作模式实现跨VPC ENI的分配和管理。

## 2. 核心流程设计

```mermaid
sequenceDiagram
    participant Pod as Pod (with CrossVPC annotations)
    participant Agent as Agent IPAM
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant Cloud as Cloud API

    Note over Pod,Cloud: 【整体流程】Agent写入 → Operator处理 → Agent返回
    
    Pod->>Agent: Pod创建，带CrossVPC注解
    Agent->>Agent: CrossVPC IPAM处理
    Agent->>CEP: 创建CEP，写入CrossVPC信息
    
    Note over CEP,Operator: 【Operator异步处理】
    Operator->>CEP: 监听CEP创建事件
    Operator->>Operator: 解析CrossVPC配置
    Operator->>Cloud: 创建/复用CrossVPC ENI
    Operator->>CEP: 回写ENI信息到CEP
    
    Note over Agent,Pod: 【Agent返回结果】
    Agent->>CEP: 等待CEP状态更新
    Agent->>Pod: 返回网络配置
```

## 3. 双模式支持

### 3.1 模式定义

| 模式 | 类型 | 使用方式 | 对应现有模式 |
|------|------|---------|-------------|
| **独占模式** | CrossVPCPrimary | Pod独占整个CrossVPC ENI | 类似Primary ENI |
| **共享模式** | CrossVPCSecondary | 多Pod共享CrossVPC ENI的不同IP | 类似Secondary IP |

### 3.2 Pod注解配置

```yaml
# 独占模式
apiVersion: v1
kind: Pod
metadata:
  annotations:
    cross-vpc-eni.cce.io/enabled: "true"
    cross-vpc-eni.cce.io/mode: "primary"  # 独占模式
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123,sg-456"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    cross-vpc-eni.cce.io/privateIPAddress: "*************"  # 可选，指定IP

# 共享模式
apiVersion: v1
kind: Pod
metadata:
  annotations:
    cross-vpc-eni.cce.io/enabled: "true"
    cross-vpc-eni.cce.io/mode: "secondary"  # 共享模式（默认）
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123,sg-456"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    # 共享模式无需指定IP，自动分配
```

## 4. 核心实现

### 4.1 Agent侧：CrossVPC IPAM

```go
// pkg/endpoint/agent_endpoint_allocator.go
func (e *endpointAllocator) allocateIP(ctx context.Context, logEntry *logrus.Entry, 
    containerID string, family ipam.Family, owner, netns string, 
    psts *ccev2.PodSubnetTopologySpread, pod *corev1.Pod, isFixedPod bool) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    // 检查是否为CrossVPC请求
    if isCrossVPCRequest(pod) {
        return e.allocateCrossVPCIP(ctx, logEntry, containerID, family, owner, netns, pod)
    }
    
    // 现有的PSTS和其他分配逻辑...
    return e.allocateRegularIP(ctx, logEntry, containerID, family, owner, netns, psts, pod, isFixedPod)
}

func (e *endpointAllocator) allocateCrossVPCIP(ctx context.Context, logEntry *logrus.Entry,
    containerID string, family ipam.Family, owner, netns string, pod *corev1.Pod) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    // 1. 解析CrossVPC配置
    crossVPCConfig, err := parseCrossVPCAnnotations(pod.Annotations)
    if err != nil {
        return nil, nil, fmt.Errorf("failed to parse CrossVPC annotations: %w", err)
    }
    
    // 2. 创建CCEEndpoint，写入CrossVPC信息
    ep := endpoint.NewEndpointTemplate(containerID, netns, pod)
    
    // 3. 设置CrossVPC分配类型
    allocType := ccev2.IPAllocTypeCrossVPCSecondary // 默认共享模式
    if crossVPCConfig.Mode == "primary" {
        allocType = ccev2.IPAllocTypeCrossVPCPrimary
    }
    
    ep.Spec.Network.IPAllocation = &ccev2.IPAllocation{
        Type:            allocType,
        NodeName:        nodeTypes.GetName(),
        ReleaseStrategy: ccev2.ReleaseStrategyTTL,
        CrossVPCConfig:  crossVPCConfig,
    }
    
    // 4. 创建endpoint，等待operator处理
    ep, err = e.cceEndpointClient.CCEEndpoints(ep.Namespace).Create(ctx, ep, metav1.CreateOptions{})
    if err != nil {
        return nil, nil, err
    }
    
    // 5. 等待operator分配完成
    return e.waitForCrossVPCIPAllocation(ctx, ep, logEntry)
}

func isCrossVPCRequest(pod *corev1.Pod) bool {
    enabled, ok := pod.Annotations["cross-vpc-eni.cce.io/enabled"]
    return ok && enabled == "true"
}

func parseCrossVPCAnnotations(annotations map[string]string) (*ccev2.CrossVPCAllocationConfig, error) {
    config := &ccev2.CrossVPCAllocationConfig{}
    
    // 解析必填字段
    if userID, ok := annotations["cross-vpc-eni.cce.io/userID"]; ok {
        config.UserID = userID
    } else {
        return nil, fmt.Errorf("missing required annotation: userID")
    }
    
    if subnetID, ok := annotations["cross-vpc-eni.cce.io/subnetID"]; ok {
        config.SubnetID = subnetID
    } else {
        return nil, fmt.Errorf("missing required annotation: subnetID")
    }
    
    if sgIDs, ok := annotations["cross-vpc-eni.cce.io/securityGroupIDs"]; ok {
        config.SecurityGroupIDs = strings.Split(sgIDs, ",")
    } else {
        return nil, fmt.Errorf("missing required annotation: securityGroupIDs")
    }
    
    if vpcCIDR, ok := annotations["cross-vpc-eni.cce.io/vpcCidr"]; ok {
        config.VPCCIDR = vpcCIDR
    } else {
        return nil, fmt.Errorf("missing required annotation: vpcCidr")
    }
    
    // 解析可选字段
    config.Mode = annotations["cross-vpc-eni.cce.io/mode"]
    if config.Mode == "" {
        config.Mode = "secondary" // 默认共享模式
    }
    
    config.PrivateIPAddress = annotations["cross-vpc-eni.cce.io/privateIPAddress"]
    config.DefaultRouteInterfaceDelegation = annotations["cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation"]
    
    if excludedCidrs, ok := annotations["cross-vpc-eni.cce.io/defaultRouteExcludedCidrs"]; ok {
        config.DefaultRouteExcludedCidrs = strings.Split(excludedCidrs, ",")
    }
    
    return config, nil
}
```

### 4.2 CCEEndpoint扩展

```go
// pkg/k8s/apis/cce.baidubce.com/v2/cce_endpoint_types.go
type IPAllocation struct {
    // 现有字段
    Type            IPAllocType     `json:"type,omitempty"`
    Pool            string          `json:"pool,omitempty"`
    PSTSName        string          `json:"pstsName,omitempty"`
    NodeName        string          `json:"nodeName,omitempty"`
    ReleaseStrategy ReleaseStrategy `json:"releaseStrategy,omitempty"`
    
    // 新增CrossVPC字段
    CrossVPCConfig  *CrossVPCAllocationConfig `json:"crossVPCConfig,omitempty"`
}

type CrossVPCAllocationConfig struct {
    UserID                           string   `json:"userID"`
    SubnetID                         string   `json:"subnetID"`
    SecurityGroupIDs                 []string `json:"securityGroupIDs"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    Mode                             string   `json:"mode"` // "primary" | "secondary"
    PrivateIPAddress                 string   `json:"privateIPAddress,omitempty"`
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

// pkg/k8s/apis/cce.baidubce.com/v2/psts.go
const (
    // 现有类型
    IPAllocTypeNil        IPAllocType = ""
    IPAllocTypeElastic    IPAllocType = "Elastic"
    IPAllocTypeFixed      IPAllocType = "Fixed"
    IPAllocTypeENIPrimary IPAllocType = "PrimaryENI"
    
    // 新增CrossVPC类型
    IPAllocTypeCrossVPCPrimary   IPAllocType = "CrossVPCPrimary"   // 独占模式
    IPAllocTypeCrossVPCSecondary IPAllocType = "CrossVPCSecondary" // 共享模式
)
```

### 4.3 Operator侧：CrossVPC分配器

```go
// pkg/endpoint/operator_crossvpc_manager_provider.go
type crossVPCAllocatorProvider struct {
    *EndpointManager
}

func (provider *crossVPCAllocatorProvider) AllocateIP(ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint) error {
    var (
        owner          = resource.Namespace + "/" + resource.Name
        status         = &resource.Status
        crossVPCConfig = resource.Spec.Network.IPAllocation.CrossVPCConfig
        allocType      = resource.Spec.Network.IPAllocation.Type
    )
    
    if crossVPCConfig == nil {
        return fmt.Errorf("CrossVPC config is required")
    }
    
    log = log.WithFields(logrus.Fields{
        "crossvpc-mode": crossVPCConfig.Mode,
        "crossvpc-type": allocType,
        "endpoint":      owner,
    })
    log.Info("start allocate CrossVPC ENI")
    
    // 获取远程操作接口
    operation, err := provider.directIPAllocator.NodeEndpoint(resource)
    if err != nil {
        return fmt.Errorf("failed to get node endpoint: %w", err)
    }
    
    // 根据模式分发处理
    switch allocType {
    case ccev2.IPAllocTypeCrossVPCPrimary:
        return provider.allocateCrossVPCPrimary(ctx, log, resource, operation)
    case ccev2.IPAllocTypeCrossVPCSecondary:
        return provider.allocateCrossVPCSecondary(ctx, log, resource, operation)
    default:
        return fmt.Errorf("unsupported CrossVPC allocation type: %s", allocType)
    }
}

// 独占模式：创建专用ENI
func (provider *crossVPCAllocatorProvider) allocateCrossVPCPrimary(
    ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint, 
    operation DirectEndpointOperation) error {
    
    crossVPCConfig := resource.Spec.Network.IPAllocation.CrossVPCConfig
    
    // 构造分配动作
    action := &DirectIPAction{
        NodeName:    resource.Spec.Network.IPAllocation.NodeName,
        Owner:       resource.Namespace + "/" + resource.Name,
        SubnetID:    crossVPCConfig.SubnetID,
        RequestedIP: crossVPCConfig.PrivateIPAddress,
        CrossVPCConfig: &DirectIPCrossVPCConfig{
            UserID:           crossVPCConfig.UserID,
            SecurityGroupIDs: crossVPCConfig.SecurityGroupIDs,
            VPCCIDR:          crossVPCConfig.VPCCIDR,
            Mode:             "primary",
        },
    }
    
    // 调用云平台API创建独占ENI
    err := operation.AllocateCrossVPCENI(ctx, action)
    if err != nil {
        return fmt.Errorf("failed to allocate CrossVPC primary ENI: %w", err)
    }
    
    // 回写结果到CEP
    resource.Status.Networking.Addressing = action.Addressing
    log.Info("CrossVPC primary ENI allocated successfully")
    return nil
}

// 共享模式：复用或创建共享ENI
func (provider *crossVPCAllocatorProvider) allocateCrossVPCSecondary(
    ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint,
    operation DirectEndpointOperation) error {
    
    crossVPCConfig := resource.Spec.Network.IPAllocation.CrossVPCConfig
    
    // 构造分配动作
    action := &DirectIPAction{
        NodeName: resource.Spec.Network.IPAllocation.NodeName,
        Owner:    resource.Namespace + "/" + resource.Name,
        SubnetID: crossVPCConfig.SubnetID,
        CrossVPCConfig: &DirectIPCrossVPCConfig{
            UserID:           crossVPCConfig.UserID,
            SecurityGroupIDs: crossVPCConfig.SecurityGroupIDs,
            VPCCIDR:          crossVPCConfig.VPCCIDR,
            Mode:             "secondary",
        },
    }
    
    // 调用云平台API分配共享ENI的辅助IP
    err := operation.AllocateCrossVPCENI(ctx, action)
    if err != nil {
        return fmt.Errorf("failed to allocate CrossVPC secondary IP: %w", err)
    }
    
    // 回写结果到CEP
    resource.Status.Networking.Addressing = action.Addressing
    log.Info("CrossVPC secondary IP allocated successfully")
    return nil
}
```

### 4.4 EndpointManager集成

```go
// pkg/endpoint/operator_endpoint_manager.go
func NewEndpointManager(getterUpdater CCEEndpointGetterUpdater, reuseIPImplement DirectIPAllocator) *EndpointManager {
    manager := &EndpointManager{
        // 现有字段...
        fixedIPProvider:           &fixedIPAllocatorProvider{manager},
        pstsAllocatorProvider:     &pstsAllocatorProvider{manager},
        crossVPCAllocatorProvider: &crossVPCAllocatorProvider{manager}, // 新增
    }
    return manager
}

func (manager *EndpointManager) Update(resource *ccev2.CCEEndpoint) error {
    // 检查endpoint类型
    if !IsFixedIPEndpoint(resource) && !IsPSTSEndpoint(resource) && !IsCrossVPCEndpoint(resource) {
        return nil
    }
    
    // 根据类型分发到不同的分配器
    if IsCrossVPCEndpoint(resource) {
        err = manager.crossVPCAllocatorProvider.AllocateIP(ctx, logEntry, newObj)
    } else if IsPSTSEndpoint(resource) {
        err = manager.pstsAllocatorProvider.AllocateIP(ctx, logEntry, newObj)
    } else if IsFixedIPEndpoint(resource) {
        err = manager.fixedIPProvider.AllocateIP(ctx, logEntry, newObj)
    }
    
    // 后续处理...
    return nil
}

func IsCrossVPCEndpoint(resource *ccev2.CCEEndpoint) bool {
    if resource.Spec.Network.IPAllocation == nil {
        return false
    }
    allocType := resource.Spec.Network.IPAllocation.Type
    return allocType == ccev2.IPAllocTypeCrossVPCPrimary || allocType == ccev2.IPAllocTypeCrossVPCSecondary
}
```

## 5. 云平台适配器

```go
// pkg/ipam/allocator/privatecloudbase/privatecloudbase.go
func (p *PrivateCloudBase) AllocateCrossVPCENI(ctx context.Context, action *DirectIPAction) error {
    crossVPCConfig := action.CrossVPCConfig
    
    switch crossVPCConfig.Mode {
    case "primary":
        return p.allocateCrossVPCPrimaryENI(ctx, action)
    case "secondary":
        return p.allocateCrossVPCSecondaryENI(ctx, action)
    default:
        return fmt.Errorf("unsupported CrossVPC mode: %s", crossVPCConfig.Mode)
    }
}

func (p *PrivateCloudBase) allocateCrossVPCPrimaryENI(ctx context.Context, action *DirectIPAction) error {
    // 创建独占CrossVPC ENI
    eniID, err := p.bceClient.CreateCrossVPCENI(ctx, &CreateCrossVPCENIArgs{
        UserID:           action.CrossVPCConfig.UserID,
        SubnetID:         action.SubnetID,
        SecurityGroupIDs: action.CrossVPCConfig.SecurityGroupIDs,
        PrivateIPAddress: action.RequestedIP,
        Mode:             "primary",
    })
    if err != nil {
        return err
    }
    
    // 构造返回结果
    action.Addressing = []*ccev2.AddressPair{{
        Family:    ccev2.IPv4Family,
        IP:        eni.PrimaryIP,
        Subnet:    action.SubnetID,
        Interface: eniID,
    }}
    return nil
}

func (p *PrivateCloudBase) allocateCrossVPCSecondaryENI(ctx context.Context, action *DirectIPAction) error {
    // 查找或创建共享CrossVPC ENI，分配辅助IP
    eni, secondaryIP, err := p.bceClient.AllocateCrossVPCSecondaryIP(ctx, &AllocateCrossVPCSecondaryIPArgs{
        UserID:           action.CrossVPCConfig.UserID,
        SubnetID:         action.SubnetID,
        SecurityGroupIDs: action.CrossVPCConfig.SecurityGroupIDs,
        NodeName:         action.NodeName,
    })
    if err != nil {
        return err
    }
    
    // 构造返回结果
    action.Addressing = []*ccev2.AddressPair{{
        Family:    ccev2.IPv4Family,
        IP:        secondaryIP,
        Subnet:    action.SubnetID,
        Interface: eni.ID,
    }}
    return nil
}
```

## 6. 实施优势

### 6.1 架构简洁
- **复用PSTS模式**：90%复用现有的CCEEndpoint + Operator架构
- **流程清晰**：Agent写入 → Operator处理 → Agent返回
- **状态管理**：完整的CCEEndpoint状态管理机制

### 6.2 双模式支持
- **独占模式**：高性能，Pod独占ENI
- **共享模式**：成本优化，多Pod共享ENI
- **灵活选择**：通过注解灵活配置

### 6.3 最大复用
- **EndpointManager**：复用现有的endpoint管理框架
- **DirectIPAllocator**：复用现有的云平台适配器接口
- **监控日志**：复用现有的监控和日志体系

这个方案通过参照PSTS的分配方式，实现了简洁而高效的CrossVPCEni功能，既支持独占模式的高性能需求，又支持共享模式的成本优化需求。
