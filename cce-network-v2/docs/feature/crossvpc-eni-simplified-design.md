# CrossVPCEni 简化实现方案

## 1. 简化设计理念

### 1.1 核心简化
- **移除IPAM复杂性**：共享模式也不需要节点级IPAM管理
- **直接子网分配**：每次都直接向子网申请一个新的IP
- **ENI状态机管理**：创建ENI CR，由ENI状态机负责挂载和删除
- **统一处理流程**：独占和共享模式使用相同的处理逻辑
- **减少状态管理**：不需要维护复杂的IP池状态
- **可配置删除延迟**：共享模式支持延迟删除，独占模式立即删除

### 1.2 模式对比

| 模式 | 处理方式 | 资源分配 | 网络配置 | 删除策略 |
|------|---------|---------|---------|---------|
| **独占模式** | 创建ENI CR | 整个ENI独占 | 直接使用ENI设备 | 立即删除 |
| **共享模式** | 直接分配子网IP | 每次申请新IP | 配置辅助IP | 延迟删除 |

## 2. 简化流程设计

### 2.1 独占模式流程

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant Cloud as Cloud API

    Pod->>Agent: 创建Pod (mode=primary)
    Agent->>CEP: 创建CEP (type=CrossVPCPrimary)
    
    Operator->>CEP: 检测CEP创建
    Operator->>Operator: 可重入检查

    alt 无现有ENI CR
        Operator->>Operator: 创建ENI CR (记录instanceID)
        Note right of Operator: ENI CR由状态机处理:<br/>- 创建ENI<br/>- 挂载到实例<br/>- 更新状态
        Operator->>CEP: 更新ENI CR名称到CEP
    else 有现有ENI CR
        Operator->>Operator: 查询ENI CR状态
        Operator->>CEP: 更新ENI状态到CEP
    end
    
    Agent->>CEP: 检测到Ready状态
    Agent->>Pod: 配置网络(直接使用ENI)
```

### 2.2 共享模式流程

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant Cloud as Cloud API

    Pod->>Agent: 创建Pod (mode=secondary)
    Agent->>CEP: 创建CEP (type=CrossVPCSecondary)
    
    Operator->>CEP: 检测CEP创建
    Operator->>Cloud: 直接向子网申请IP
    Note right of Cloud: AllocatePrivateIP(subnetID, userID)
    Cloud-->>Operator: 返回IP信息
    Operator->>CEP: 更新IP信息
    
    Agent->>CEP: 检测到Ready状态
    Agent->>Pod: 配置网络(辅助IP模式)
```

## 3. 简化实现

### 3.1 Operator处理逻辑

```go
// 统一的CrossVPC处理入口
func (c *CrossVPCController) handleCrossVPCAllocation(ctx context.Context, cep *ccev2.CCEEndpoint) (ctrl.Result, error) {
    allocType := cep.Spec.Network.IPAllocation.Type
    
    switch allocType {
    case ccev2.IPAllocTypeCrossVPCPrimary:
        return c.handlePrimaryMode(ctx, cep)
    case ccev2.IPAllocTypeCrossVPCSecondary:
        return c.handleSecondaryMode(ctx, cep)
    default:
        return c.setError(cep, "UNSUPPORTED_TYPE", "Unsupported allocation type")
    }
}

// 独占模式：创建专用ENI
func (c *CrossVPCController) handlePrimaryMode(ctx context.Context, cep *ccev2.CCEEndpoint) (ctrl.Result, error) {
    config := cep.Spec.Network.IPAllocation.CrossVPCConfig
    
    // 可重入检查
    if cep.Status.ENIID != "" {
        return c.checkENIStatus(ctx, cep)
    }
    
    // 创建ENI CR，由ENI状态机处理实际创建和挂载
    eniCR, err := c.createENICR(ctx, cep, "primary")
    if err != nil {
        return c.handleCloudError(ctx, cep, err)
    }

    // 更新CEP状态
    cep.Status.ENICRName = eniCR.Name
    cep.Status.State = "Creating"
    c.Update(ctx, cep)

    // 延迟重试检查ENI CR状态
    return ctrl.Result{RequeueAfter: 1 * time.Second}, nil
}

// 共享模式：直接申请子网IP
func (c *CrossVPCController) handleSecondaryMode(ctx context.Context, cep *ccev2.CCEEndpoint) (ctrl.Result, error) {
    config := cep.Spec.Network.IPAllocation.CrossVPCConfig
    
    // 可重入检查
    if cep.Status.IP != "" {
        return c.checkIPStatus(ctx, cep)
    }
    
    // 直接向子网申请IP
    ip, err := c.cloudAPI.AllocateCrossVPCIP(ctx, &AllocateCrossVPCIPRequest{
        Mode:           "secondary",
        UserID:         config.UserID,
        SubnetID:       config.SubnetID,
        SecurityGroups: config.SecurityGroupIDs,
        NodeID:         c.nodeID,
    })
    
    if err != nil {
        return c.handleCloudError(ctx, cep, err)
    }
    
    // 更新CEP状态
    cep.Status.IP = ip.Address
    cep.Status.Gateway = ip.Gateway
    cep.Status.State = "Ready"
    c.Update(ctx, cep)
    
    return ctrl.Result{}, nil
}

// 检查ENI CR状态
func (c *CrossVPCController) checkENICRStatus(ctx context.Context, cep *ccev2.CCEEndpoint) (ctrl.Result, error) {
    eniCR, err := c.eniClient.ENIs().Get(ctx, cep.Status.ENICRName, metav1.GetOptions{})
    if err != nil {
        return c.handleCloudError(ctx, cep, err)
    }

    switch eniCR.Status.AttachmentStatus.Phase {
    case "Attached":
        // ENI已挂载，更新CEP状态
        cep.Status.ENIID = eniCR.Status.ENI.ID
        cep.Status.MACAddress = eniCR.Status.ENI.MACAddress
        cep.Status.IP = eniCR.Status.ENI.PrimaryIP
        cep.Status.State = "Ready"
        c.Update(ctx, cep)
        return ctrl.Result{}, nil

    case "Pending", "Attaching":
        return ctrl.Result{RequeueAfter: 1 * time.Second}, nil

    case "Failed":
        return c.setError(cep, "ENI_FAILED", eniCR.Status.AttachmentStatus.Message)

    default:
        return ctrl.Result{RequeueAfter: 1 * time.Second}, nil
    }
}

// 检查IP状态
func (c *CrossVPCController) checkIPStatus(ctx context.Context, cep *ccev2.CCEEndpoint) (ctrl.Result, error) {
    // 共享模式的IP一旦分配就是Ready状态
    if cep.Status.State != "Ready" {
        cep.Status.State = "Ready"
        c.Update(ctx, cep)
    }
    return ctrl.Result{}, nil
}
```

### 3.2 云平台API简化

```go
// 云平台API接口
type CloudAPI interface {
    // 创建CrossVPC ENI（独占模式）
    CreateCrossVPCENI(ctx context.Context, req *CreateCrossVPCENIRequest) (*ENIInfo, error)
    
    // 分配CrossVPC IP（共享模式）
    AllocateCrossVPCIP(ctx context.Context, req *AllocateCrossVPCIPRequest) (*IPInfo, error)
    
    // 查询ENI状态
    GetENI(ctx context.Context, eniID string) (*ENIInfo, error)
    
    // 释放资源
    ReleaseCrossVPCENI(ctx context.Context, eniID string) error
    ReleaseCrossVPCIP(ctx context.Context, ip string) error
}

// 创建ENI请求
type CreateCrossVPCENIRequest struct {
    Mode           string   // "primary"
    InstanceID     string
    UserID         string
    SubnetID       string
    SecurityGroups []string
    PrivateIP      string   // 可选
}

// 分配IP请求
type AllocateCrossVPCIPRequest struct {
    Mode           string   // "secondary"
    UserID         string
    SubnetID       string
    SecurityGroups []string
    NodeID         string
}

// ENI信息
type ENIInfo struct {
    ID         string
    MACAddress string
    PrimaryIP  string
    Status     string
}

// IP信息
type IPInfo struct {
    Address string
    Gateway string
    Subnet  string
}
```

### 3.3 Agent侧简化

```go
// Agent侧处理
func (e *endpointAllocator) allocateCrossVPCIP(ctx context.Context, pod *corev1.Pod) (*models.IPAMAddressResponse, error) {
    // 1. 解析配置
    config, err := parseCrossVPCAnnotations(pod.Annotations)
    if err != nil {
        return nil, err
    }
    
    // 2. 创建CEP
    ep := &ccev2.CCEEndpoint{
        ObjectMeta: metav1.ObjectMeta{
            Name:      pod.Name,
            Namespace: pod.Namespace,
        },
        Spec: ccev2.CCEEndpointSpec{
            Network: ccev2.EndpointNetworking{
                IPAllocation: &ccev2.IPAllocation{
                    Type:           getAllocationType(config.Mode),
                    NodeName:       nodeTypes.GetName(),
                    CrossVPCConfig: config,
                },
            },
        },
    }
    
    // 3. 创建CEP，等待Operator处理
    ep, err = e.cceEndpointClient.CCEEndpoints(ep.Namespace).Create(ctx, ep, metav1.CreateOptions{})
    if err != nil {
        return nil, err
    }
    
    // 4. 等待分配完成
    return e.waitForAllocation(ctx, ep)
}

func getAllocationType(mode string) ccev2.IPAllocType {
    if mode == "primary" {
        return ccev2.IPAllocTypeCrossVPCPrimary
    }
    return ccev2.IPAllocTypeCrossVPCSecondary
}

func (e *endpointAllocator) waitForAllocation(ctx context.Context, ep *ccev2.CCEEndpoint) (*models.IPAMAddressResponse, error) {
    timeout := 5 * time.Minute
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()
    
    ticker := time.NewTicker(2 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-timeoutCtx.Done():
            return nil, fmt.Errorf("timeout waiting for CrossVPC allocation")
            
        case <-ticker.C:
            currentEP, err := e.cceEndpointClient.CCEEndpoints(ep.Namespace).Get(ctx, ep.Name, metav1.GetOptions{})
            if err != nil {
                continue
            }
            
            if currentEP.Status.State == "Ready" && currentEP.Status.IP != "" {
                return &models.IPAMAddressResponse{
                    Address: &models.AddressPair{
                        IPV4: currentEP.Status.IP,
                    },
                    Interface:   currentEP.Status.ENIID,     // 独占模式有值，共享模式为空
                    MACAddress:  currentEP.Status.MACAddress, // 独占模式有值，共享模式为空
                    Gateway:     currentEP.Status.Gateway,
                }, nil
            }
            
            if currentEP.Status.State == "Failed" {
                return nil, fmt.Errorf("CrossVPC allocation failed: %s", currentEP.Status.ErrorMessage)
            }
        }
    }
}
```

### 3.4 CNI插件简化

```go
// CNI插件处理
func configureCrossVPCNetwork(args *skel.CmdArgs, ipam *models.IPAMAddressResponse) error {
    if ipam.Interface != "" && ipam.MACAddress != "" {
        // 独占模式：直接使用ENI设备
        return configureDirectENI(args, ipam)
    } else {
        // 共享模式：配置辅助IP
        return configureSecondaryIP(args, ipam)
    }
}

func configureDirectENI(args *skel.CmdArgs, ipam *models.IPAMAddressResponse) error {
    // 根据MAC地址找到ENI设备
    eniDevice, err := findENIByMAC(ipam.MACAddress)
    if err != nil {
        return fmt.Errorf("failed to find ENI device by MAC %s: %w", ipam.MACAddress, err)
    }
    
    // 移动ENI设备到容器命名空间
    return moveENIToContainer(args.Netns, eniDevice, ipam.Address.IPV4, ipam.Gateway)
}

func configureSecondaryIP(args *skel.CmdArgs, ipam *models.IPAMAddressResponse) error {
    // 创建veth pair或使用其他方式配置辅助IP
    return setupSecondaryIPNetwork(args.Netns, ipam.Address.IPV4, ipam.Gateway)
}
```

## 4. 删除流程设计

### 4.1 独占模式删除流程

```mermaid
sequenceDiagram
    participant Pod as Pod删除
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over Pod,Cloud: 【独占模式删除 - 立即删除】
    Pod->>Agent: Pod删除事件
    Agent->>CEP: 标记CEP删除

    Operator->>CEP: 检测CEP删除
    Operator->>ENIC: 标记ENI CR删除 (deletionTimestamp)
    Note right of ENIC: deletionPolicy.mode = "immediate"

    ENISM->>ENIC: 检测deletionTimestamp
    ENISM->>ENISM: 检查删除策略 (immediate)
    ENISM->>Cloud: 分离ENI
    Note right of Cloud: DetachENI(eniID, instanceID)

    loop 等待分离完成
        ENISM->>Cloud: 查询ENI状态
        Cloud-->>ENISM: 返回状态
        alt 分离完成
            ENISM->>Cloud: 删除ENI
            Cloud-->>ENISM: 删除成功
            ENISM->>ENIC: 移除finalizer
        else 分离中
            ENISM->>ENISM: 等待1秒后重试
        end
    end

    ENIC->>ENIC: ENI CR被删除
    Operator->>CEP: 清理CEP资源
```

### 4.2 共享模式删除流程

```mermaid
sequenceDiagram
    participant Pod1 as Pod1删除
    participant Pod2 as Pod2删除
    participant Agent as Agent
    participant CEP1 as CEP1
    participant CEP2 as CEP2
    participant Operator as Operator
    participant Cloud as Cloud API

    Note over Pod1,Cloud: 【共享模式删除 - 延迟删除】

    Note over Pod1,CEP1: 【第一个Pod删除】
    Pod1->>Agent: Pod1删除事件
    Agent->>CEP1: 标记CEP1删除

    Operator->>CEP1: 检测CEP1删除
    Operator->>Cloud: 释放IP1
    Note right of Cloud: ReleasePrivateIP(ip1)
    Cloud-->>Operator: 释放成功
    Operator->>CEP1: 清理CEP1资源

    Note over Pod2,CEP2: 【第二个Pod删除 - 触发延迟删除】
    Pod2->>Agent: Pod2删除事件
    Agent->>CEP2: 标记CEP2删除

    Operator->>CEP2: 检测CEP2删除
    Operator->>Cloud: 释放IP2 (最后一个IP)
    Cloud-->>Operator: 释放成功

    Note over Operator,Cloud: 【延迟删除机制】
    Operator->>Operator: 检查是否为最后一个IP
    Operator->>Operator: 启动延迟删除定时器
    Note right of Operator: 等待配置的延迟时间<br/>(默认5分钟)

    loop 延迟期间检查
        Operator->>Operator: 检查是否有新的IP分配
        alt 有新IP分配
            Operator->>Operator: 取消删除定时器
        else 延迟时间到达
            Operator->>Cloud: 删除ENI
            Cloud-->>Operator: 删除成功
        end
    end

    Operator->>CEP2: 清理CEP2资源
```

## 5. 资源清理简化

### 5.1 删除处理实现

```go
// Operator删除处理
func (c *CrossVPCController) handleDeletion(ctx context.Context, cep *ccev2.CCEEndpoint) error {
    allocType := cep.Spec.Network.IPAllocation.Type

    switch allocType {
    case ccev2.IPAllocTypeCrossVPCPrimary:
        return c.handlePrimaryDeletion(ctx, cep)
    case ccev2.IPAllocTypeCrossVPCSecondary:
        return c.handleSecondaryDeletion(ctx, cep)
    }

    return nil
}

// 独占模式删除：标记ENI CR删除
func (c *CrossVPCController) handlePrimaryDeletion(ctx context.Context, cep *ccev2.CCEEndpoint) error {
    if cep.Status.ENICRName == "" {
        return nil // 没有ENI CR需要清理
    }

    // 标记ENI CR删除，由ENI状态机处理实际删除
    err := c.eniClient.ENIs().Delete(ctx, cep.Status.ENICRName, metav1.DeleteOptions{})
    if err != nil && !errors.IsNotFound(err) {
        return fmt.Errorf("failed to mark ENI CR for deletion: %w", err)
    }

    // 等待ENI CR删除完成
    return c.waitForENICRDeletion(ctx, cep.Status.ENICRName)
}

// 共享模式删除：直接释放IP，检查是否需要删除ENI
func (c *CrossVPCController) handleSecondaryDeletion(ctx context.Context, cep *ccev2.CCEEndpoint) error {
    if cep.Status.IP == "" {
        return nil // 没有IP需要释放
    }

    // 1. 释放分配的IP
    err := c.cloudAPI.ReleaseCrossVPCIP(ctx, cep.Status.IP)
    if err != nil {
        return fmt.Errorf("failed to release CrossVPC IP: %w", err)
    }

    // 2. 检查是否为最后一个使用该ENI的Pod
    return c.checkAndScheduleENIDeletion(ctx, cep)
}

// 检查并调度ENI删除
func (c *CrossVPCController) checkAndScheduleENIDeletion(ctx context.Context, cep *ccev2.CCEEndpoint) error {
    config := cep.Spec.Network.IPAllocation.CrossVPCConfig

    // 查找同一ENI的其他CEP
    otherCEPs, err := c.findCEPsUsingSameENI(ctx, config)
    if err != nil {
        return err
    }

    if len(otherCEPs) == 0 {
        // 这是最后一个使用该ENI的Pod，启动延迟删除
        return c.scheduleENIDeletion(ctx, config)
    }

    return nil
}

// 调度ENI延迟删除
func (c *CrossVPCController) scheduleENIDeletion(ctx context.Context, config *ccev2.CrossVPCAllocationConfig) error {
    delaySeconds := c.getSharedENIDeletionDelay()

    // 创建延迟删除任务
    deletionTask := &ENIDeletionTask{
        UserID:       config.UserID,
        SubnetID:     config.SubnetID,
        ScheduledAt:  time.Now().Add(time.Duration(delaySeconds) * time.Second),
        Config:       config,
    }

    // 添加到延迟删除队列
    c.deletionQueue.Add(deletionTask)

    log.WithFields(logrus.Fields{
        "userID":       config.UserID,
        "subnetID":     config.SubnetID,
        "delaySeconds": delaySeconds,
    }).Info("scheduled ENI deletion")

    return nil
}

// 延迟删除任务处理
func (c *CrossVPCController) processDeletionQueue() {
    ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
    defer ticker.Stop()

    for range ticker.C {
        c.processScheduledDeletions()
    }
}

func (c *CrossVPCController) processScheduledDeletions() {
    now := time.Now()

    for _, task := range c.deletionQueue.GetDueTasks(now) {
        // 再次检查是否有新的IP分配
        ceps, err := c.findCEPsUsingSameENI(context.Background(), task.Config)
        if err != nil {
            log.WithError(err).Error("failed to check CEPs during deletion")
            continue
        }

        if len(ceps) > 0 {
            // 有新的IP分配，取消删除
            log.WithFields(logrus.Fields{
                "userID":   task.UserID,
                "subnetID": task.SubnetID,
            }).Info("cancelled ENI deletion due to new IP allocation")
            c.deletionQueue.Remove(task)
            continue
        }

        // 执行ENI删除
        err = c.deleteSharedENI(context.Background(), task.Config)
        if err != nil {
            log.WithError(err).Error("failed to delete shared ENI")
            // 重新调度删除（延长时间）
            task.ScheduledAt = now.Add(1 * time.Minute)
        } else {
            log.WithFields(logrus.Fields{
                "userID":   task.UserID,
                "subnetID": task.SubnetID,
            }).Info("shared ENI deleted successfully")
            c.deletionQueue.Remove(task)
        }
    }
}

// 删除共享ENI
func (c *CrossVPCController) deleteSharedENI(ctx context.Context, config *ccev2.CrossVPCAllocationConfig) error {
    // 查找对应的ENI
    eni, err := c.findSharedENI(ctx, config)
    if err != nil {
        return err
    }

    if eni == nil {
        return nil // ENI已经不存在
    }

    // 删除ENI
    return c.cloudAPI.ReleaseCrossVPCENI(ctx, eni.ID)
}
```

### 5.2 ENI状态机删除处理

```go
// ENI状态机删除处理
func (esm *eniStateMachine) handleDeletion() error {
    eni := esm.resource
    deletionPolicy := eni.Spec.DeletionPolicy

    if deletionPolicy == nil || deletionPolicy.Mode == "immediate" {
        // 独占模式或默认：立即删除
        return esm.performImmediateDeletion()
    }

    // 共享模式：延迟删除（实际上共享模式的ENI由Operator管理删除）
    return esm.performImmediateDeletion()
}

func (esm *eniStateMachine) performImmediateDeletion() error {
    eni := esm.resource
    eniID := eni.Status.ENI.ID
    instanceID := eni.Spec.InstanceID

    switch eni.Status.AttachmentStatus.Phase {
    case "Attached":
        // 1. 分离ENI
        log.WithField("eniID", eniID).Info("detaching ENI")
        err := esm.bceClient.DetachENI(context.Background(), &enisdk.DetachEniArgs{
            EniId:      eniID,
            InstanceId: instanceID,
        })
        if err != nil {
            return fmt.Errorf("failed to detach ENI: %w", err)
        }

        eni.Status.AttachmentStatus.Phase = "Detaching"
        eni.Status.AttachmentStatus.Message = "ENI detachment in progress"
        eni.Status.AttachmentStatus.LastUpdated = time.Now().Format(time.RFC3339)
        return nil // 等待下次reconcile检查分离状态

    case "Detaching":
        // 2. 检查分离状态
        cloudENI, err := esm.bceClient.GetENI(context.Background(), eniID)
        if err != nil {
            return fmt.Errorf("failed to get ENI status: %w", err)
        }

        if cloudENI.InstanceID == "" && cloudENI.Status == "available" {
            eni.Status.AttachmentStatus.Phase = "Detached"
            eni.Status.AttachmentStatus.Message = "ENI detached successfully"
            eni.Status.AttachmentStatus.LastUpdated = time.Now().Format(time.RFC3339)
        } else {
            return nil // 继续等待分离完成
        }

    case "Detached":
        // 3. 删除ENI
        log.WithField("eniID", eniID).Info("deleting ENI")
        err := esm.bceClient.DeleteENI(context.Background(), eniID)
        if err != nil {
            return fmt.Errorf("failed to delete ENI: %w", err)
        }

        // 移除finalizer，允许删除ENI CR
        eni.Finalizers = removeFinalizer(eni.Finalizers, "eni.cce.baidubce.com/cleanup")
        log.WithField("eniID", eniID).Info("ENI deleted successfully")

    default:
        // 其他状态，直接删除
        if eniID != "" {
            err := esm.bceClient.DeleteENI(context.Background(), eniID)
            if err != nil && !isENINotFoundError(err) {
                return fmt.Errorf("failed to delete ENI: %w", err)
            }
        }

        eni.Finalizers = removeFinalizer(eni.Finalizers, "eni.cce.baidubce.com/cleanup")
    }

    return nil
}

// 辅助函数
func removeFinalizer(finalizers []string, finalizer string) []string {
    var result []string
    for _, f := range finalizers {
        if f != finalizer {
            result = append(result, f)
        }
    }
    return result
}

func isENINotFoundError(err error) bool {
    if bceErr, ok := err.(*bce.BceServiceError); ok {
        return bceErr.Code == "InvalidEniId.NotFound"
    }
    return false
}
```

### 5.3 删除队列管理

```go
// 延迟删除任务
type ENIDeletionTask struct {
    UserID      string
    SubnetID    string
    ScheduledAt time.Time
    Config      *ccev2.CrossVPCAllocationConfig
}

// 删除队列
type DeletionQueue struct {
    tasks []ENIDeletionTask
    mutex sync.RWMutex
}

func (q *DeletionQueue) Add(task *ENIDeletionTask) {
    q.mutex.Lock()
    defer q.mutex.Unlock()

    // 检查是否已存在相同的任务
    for i, existingTask := range q.tasks {
        if existingTask.UserID == task.UserID && existingTask.SubnetID == task.SubnetID {
            // 更新现有任务的调度时间
            q.tasks[i].ScheduledAt = task.ScheduledAt
            return
        }
    }

    // 添加新任务
    q.tasks = append(q.tasks, *task)
}

func (q *DeletionQueue) GetDueTasks(now time.Time) []ENIDeletionTask {
    q.mutex.RLock()
    defer q.mutex.RUnlock()

    var dueTasks []ENIDeletionTask
    for _, task := range q.tasks {
        if now.After(task.ScheduledAt) {
            dueTasks = append(dueTasks, task)
        }
    }

    return dueTasks
}

func (q *DeletionQueue) Remove(task *ENIDeletionTask) {
    q.mutex.Lock()
    defer q.mutex.Unlock()

    for i, existingTask := range q.tasks {
        if existingTask.UserID == task.UserID && existingTask.SubnetID == task.SubnetID {
            q.tasks = append(q.tasks[:i], q.tasks[i+1:]...)
            break
        }
    }
}
```

## 5. 简化方案优势

### 5.1 实现简化
- **无IPAM复杂性**：不需要维护节点级IP池
- **无状态管理**：不需要复杂的IP分配状态
- **统一处理**：两种模式使用相似的处理逻辑
- **直接分配**：每次都是新的资源分配，避免复用冲突

### 5.2 运维简化
- **故障排查简单**：每个Pod对应明确的资源
- **资源清理简单**：Pod删除时直接清理对应资源
- **监控简化**：只需要监控分配成功率和延迟

### 5.3 性能考虑
- **分配延迟**：每次都需要调用云平台API，可能略慢
- **资源利用**：共享模式不复用ENI，可能资源利用率较低
- **并发性能**：无状态管理，并发处理更简单

### 5.4 ENI状态机优势
- **分离关注点**：Operator专注业务逻辑，ENI状态机专注资源管理
- **灵活删除策略**：独占模式立即删除，共享模式可配置延迟删除
- **状态可观测**：详细的ENI挂载和删除状态，便于运维监控

### 5.5 删除策略配置

```yaml
# ConfigMap配置示例
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  # CrossVPC ENI删除配置
  crossvpc-eni-shared-deletion-delay: "300"    # 共享模式延迟5分钟删除
  crossvpc-eni-primary-deletion-delay: "0"     # 独占模式立即删除
```

这个简化方案通过移除IPAM复杂性、引入ENI状态机管理和可配置删除策略，在大大简化实现复杂度的同时，提供了更加灵活和可靠的资源管理能力。
