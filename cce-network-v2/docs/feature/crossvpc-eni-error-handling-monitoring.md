# CrossVPCEni 错误处理和监控方案

## 1. 错误处理策略

### 1.1 分层错误处理

```mermaid
flowchart TD
    A[Pod创建] --> B[Agent IPAM]
    B --> C{解析注解}
    C -->|失败| D[返回配置错误]
    C -->|成功| E[创建CEP]
    E --> F{CEP创建}
    F -->|失败| G[返回K8s API错误]
    F -->|成功| H[Operator处理]
    H --> I{云平台分配}
    I -->|失败| J[更新CEP错误状态]
    I -->|成功| K[更新CEP成功状态]
    J --> L[Agent检测错误]
    K --> M[Agent检测成功]
    L --> N[返回分配失败]
    M --> O[配置网络]
    
    style D fill:#ffcdd2
    style G fill:#ffcdd2
    style J fill:#ffcdd2
    style L fill:#ffcdd2
    style N fill:#ffcdd2
```

### 1.2 错误分类和处理

#### 1.2.1 配置错误（Agent侧）

```go
// pkg/endpoint/agent_endpoint_allocator.go
type CrossVPCConfigError struct {
    Field   string
    Value   string
    Message string
}

func (e CrossVPCConfigError) Error() string {
    return fmt.Sprintf("CrossVPC config error in field '%s' (value: '%s'): %s", e.Field, e.Value, e.Message)
}

func validateCrossVPCConfig(config *ccev2.CrossVPCAllocationConfig) error {
    var errors []string
    
    // 验证UserID
    if config.UserID == "" {
        errors = append(errors, "userID is required")
    } else if !isValidUserID(config.UserID) {
        errors = append(errors, "userID format is invalid")
    }
    
    // 验证SubnetID
    if config.SubnetID == "" {
        errors = append(errors, "subnetID is required")
    } else if !isValidSubnetID(config.SubnetID) {
        errors = append(errors, "subnetID format is invalid")
    }
    
    // 验证SecurityGroupIDs
    if len(config.SecurityGroupIDs) == 0 {
        errors = append(errors, "at least one securityGroupID is required")
    } else {
        for _, sgID := range config.SecurityGroupIDs {
            if !isValidSecurityGroupID(sgID) {
                errors = append(errors, fmt.Sprintf("invalid securityGroupID: %s", sgID))
            }
        }
    }
    
    // 验证VPCCIDR
    if config.VPCCIDR == "" {
        errors = append(errors, "vpcCIDR is required")
    } else if !isValidCIDR(config.VPCCIDR) {
        errors = append(errors, "vpcCIDR format is invalid")
    }
    
    // 验证PrivateIPAddress（如果指定）
    if config.PrivateIPAddress != "" && !isValidIPAddress(config.PrivateIPAddress) {
        errors = append(errors, "privateIPAddress format is invalid")
    }
    
    // 验证Mode
    if config.Mode != "" && config.Mode != "primary" && config.Mode != "secondary" {
        errors = append(errors, "mode must be 'primary' or 'secondary'")
    }
    
    if len(errors) > 0 {
        return fmt.Errorf("CrossVPC config validation failed: %s", strings.Join(errors, "; "))
    }
    
    return nil
}

func isValidUserID(userID string) bool {
    // 用户ID格式验证逻辑
    return regexp.MustCompile(`^[a-zA-Z0-9]{6,32}$`).MatchString(userID)
}

func isValidSubnetID(subnetID string) bool {
    // 子网ID格式验证逻辑
    return regexp.MustCompile(`^sbn-[a-zA-Z0-9]{8,}$`).MatchString(subnetID)
}

func isValidSecurityGroupID(sgID string) bool {
    // 安全组ID格式验证逻辑
    return regexp.MustCompile(`^sg-[a-zA-Z0-9]{8,}$`).MatchString(sgID)
}

func isValidCIDR(cidr string) bool {
    _, _, err := net.ParseCIDR(cidr)
    return err == nil
}

func isValidIPAddress(ip string) bool {
    return net.ParseIP(ip) != nil
}
```

#### 1.2.2 云平台错误（Operator侧）

```go
// pkg/endpoint/operator_crossvpc_manager_provider.go
type CloudPlatformError struct {
    Operation string
    Code      string
    Message   string
    Retryable bool
}

func (e CloudPlatformError) Error() string {
    return fmt.Sprintf("cloud platform error in %s (code: %s): %s", e.Operation, e.Code, e.Message)
}

func (e CloudPlatformError) IsRetryable() bool {
    return e.retryable
}

func handleCloudPlatformError(err error, operation string) error {
    if err == nil {
        return nil
    }
    
    // 解析云平台错误
    if bceErr, ok := err.(*bce.BceServiceError); ok {
        switch bceErr.Code {
        case "QuotaExceeded":
            return CloudPlatformError{
                Operation: operation,
                Code:      bceErr.Code,
                Message:   "ENI quota exceeded",
                Retryable: false,
            }
        case "InsufficientCapacity":
            return CloudPlatformError{
                Operation: operation,
                Code:      bceErr.Code,
                Message:   "Insufficient capacity in subnet",
                Retryable: true,
            }
        case "InvalidSubnet":
            return CloudPlatformError{
                Operation: operation,
                Code:      bceErr.Code,
                Message:   "Invalid subnet configuration",
                Retryable: false,
            }
        case "SecurityGroupNotFound":
            return CloudPlatformError{
                Operation: operation,
                Code:      bceErr.Code,
                Message:   "Security group not found",
                Retryable: false,
            }
        case "ThrottlingException":
            return CloudPlatformError{
                Operation: operation,
                Code:      bceErr.Code,
                Message:   "API rate limit exceeded",
                Retryable: true,
            }
        default:
            return CloudPlatformError{
                Operation: operation,
                Code:      bceErr.Code,
                Message:   bceErr.Message,
                Retryable: isRetryableError(bceErr.Code),
            }
        }
    }
    
    return err
}

func isRetryableError(code string) bool {
    retryableCodes := []string{
        "InternalError",
        "ServiceUnavailable",
        "ThrottlingException",
        "InsufficientCapacity",
        "RequestTimeout",
    }
    
    for _, retryableCode := range retryableCodes {
        if code == retryableCode {
            return true
        }
    }
    return false
}
```

#### 1.2.3 重试机制

```go
// pkg/endpoint/retry.go
type RetryConfig struct {
    MaxRetries    int
    InitialDelay  time.Duration
    MaxDelay      time.Duration
    BackoffFactor float64
}

func NewDefaultRetryConfig() *RetryConfig {
    return &RetryConfig{
        MaxRetries:    3,
        InitialDelay:  1 * time.Second,
        MaxDelay:      30 * time.Second,
        BackoffFactor: 2.0,
    }
}

func RetryWithBackoff(ctx context.Context, config *RetryConfig, operation func() error) error {
    var lastErr error
    delay := config.InitialDelay
    
    for attempt := 0; attempt <= config.MaxRetries; attempt++ {
        if attempt > 0 {
            // 等待退避时间
            select {
            case <-ctx.Done():
                return ctx.Err()
            case <-time.After(delay):
            }
            
            // 计算下次退避时间
            delay = time.Duration(float64(delay) * config.BackoffFactor)
            if delay > config.MaxDelay {
                delay = config.MaxDelay
            }
        }
        
        err := operation()
        if err == nil {
            return nil
        }
        
        lastErr = err
        
        // 检查是否可重试
        if cloudErr, ok := err.(CloudPlatformError); ok && !cloudErr.IsRetryable() {
            return err // 不可重试的错误，直接返回
        }
        
        log.WithFields(logrus.Fields{
            "attempt": attempt + 1,
            "maxRetries": config.MaxRetries,
            "delay": delay,
            "error": err.Error(),
        }).Warn("operation failed, retrying")
    }
    
    return fmt.Errorf("operation failed after %d retries: %w", config.MaxRetries, lastErr)
}

// 在Operator中使用重试机制
func (provider *crossVPCAllocatorProvider) allocateCrossVPCPrimaryWithRetry(
    ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint, 
    operation DirectEndpointOperation) error {
    
    retryConfig := NewDefaultRetryConfig()
    
    return RetryWithBackoff(ctx, retryConfig, func() error {
        return provider.allocateCrossVPCPrimary(ctx, log, resource, operation)
    })
}
```

## 2. 监控指标

### 2.1 核心监控指标

```go
// pkg/metrics/crossvpc_metrics.go
package metrics

import (
    "time"
    "github.com/prometheus/client_golang/prometheus"
)

var (
    // 分配相关指标
    crossVPCEniAllocationTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cce_crossvpc_eni_allocation_total",
            Help: "Total number of CrossVPC ENI allocation attempts",
        },
        []string{"node", "mode", "user_id", "result"}, // result: success/failed
    )
    
    crossVPCEniAllocationDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "cce_crossvpc_eni_allocation_duration_seconds",
            Help:    "Duration of CrossVPC ENI allocation",
            Buckets: []float64{1, 5, 10, 30, 60, 120, 300}, // 1s到5min
        },
        []string{"node", "mode", "user_id"},
    )
    
    // 资源使用指标
    crossVPCEniPoolUtilization = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "cce_crossvpc_eni_pool_utilization_ratio",
            Help: "CrossVPC ENI pool utilization ratio (0-1)",
        },
        []string{"node", "subnet_id", "user_id"},
    )
    
    crossVPCEniActiveCount = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "cce_crossvpc_eni_active_count",
            Help: "Number of active CrossVPC ENIs",
        },
        []string{"node", "mode", "user_id"},
    )
    
    // 错误相关指标
    crossVPCEniErrorTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cce_crossvpc_eni_error_total",
            Help: "Total number of CrossVPC ENI errors",
        },
        []string{"node", "error_type", "error_code"},
    )
    
    // 云平台API调用指标
    crossVPCCloudAPICallTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cce_crossvpc_cloud_api_call_total",
            Help: "Total number of cloud API calls for CrossVPC ENI",
        },
        []string{"node", "api_operation", "result"},
    )
    
    crossVPCCloudAPICallDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "cce_crossvpc_cloud_api_call_duration_seconds",
            Help:    "Duration of cloud API calls for CrossVPC ENI",
            Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30},
        },
        []string{"node", "api_operation"},
    )
)

func init() {
    prometheus.MustRegister(
        crossVPCEniAllocationTotal,
        crossVPCEniAllocationDuration,
        crossVPCEniPoolUtilization,
        crossVPCEniActiveCount,
        crossVPCEniErrorTotal,
        crossVPCCloudAPICallTotal,
        crossVPCCloudAPICallDuration,
    )
}

// 记录分配结果
func RecordCrossVPCEniAllocation(node, mode, userID, result string, duration time.Duration) {
    crossVPCEniAllocationTotal.WithLabelValues(node, mode, userID, result).Inc()
    if result == "success" {
        crossVPCEniAllocationDuration.WithLabelValues(node, mode, userID).Observe(duration.Seconds())
    }
}

// 记录错误
func RecordCrossVPCEniError(node, errorType, errorCode string) {
    crossVPCEniErrorTotal.WithLabelValues(node, errorType, errorCode).Inc()
}

// 记录云平台API调用
func RecordCrossVPCCloudAPICall(node, operation, result string, duration time.Duration) {
    crossVPCCloudAPICallTotal.WithLabelValues(node, operation, result).Inc()
    crossVPCCloudAPICallDuration.WithLabelValues(node, operation).Observe(duration.Seconds())
}

// 更新资源使用情况
func UpdateCrossVPCEniPoolUtilization(node, subnetID, userID string, utilization float64) {
    crossVPCEniPoolUtilization.WithLabelValues(node, subnetID, userID).Set(utilization)
}

func UpdateCrossVPCEniActiveCount(node, mode, userID string, count int) {
    crossVPCEniActiveCount.WithLabelValues(node, mode, userID).Set(float64(count))
}
```

### 2.2 监控指标使用示例

```go
// 在Operator中记录指标
func (provider *crossVPCAllocatorProvider) AllocateIP(ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint) error {
    startTime := time.Now()
    crossVPCConfig := resource.Spec.Network.IPAllocation.CrossVPCConfig
    nodeName := resource.Spec.Network.IPAllocation.NodeName
    
    var result string
    defer func() {
        duration := time.Since(startTime)
        RecordCrossVPCEniAllocation(nodeName, crossVPCConfig.Mode, crossVPCConfig.UserID, result, duration)
    }()
    
    err := provider.allocateIPInternal(ctx, log, resource)
    if err != nil {
        result = "failed"
        // 记录错误类型
        if cloudErr, ok := err.(CloudPlatformError); ok {
            RecordCrossVPCEniError(nodeName, "cloud_platform", cloudErr.Code)
        } else {
            RecordCrossVPCEniError(nodeName, "internal", "unknown")
        }
        return err
    }
    
    result = "success"
    return nil
}

// 在云平台适配器中记录API调用
func (p *PrivateCloudBase) createENIWithMetrics(ctx context.Context, args *enisdk.CreateEniArgs) (string, error) {
    startTime := time.Now()
    operation := "CreateENI"
    
    var result string
    defer func() {
        duration := time.Since(startTime)
        RecordCrossVPCCloudAPICall(p.nodeName, operation, result, duration)
    }()
    
    eniID, err := p.bceClient.CreateENI(ctx, args)
    if err != nil {
        result = "failed"
        return "", err
    }
    
    result = "success"
    return eniID, nil
}
```

## 3. 告警规则

### 3.1 Prometheus告警规则

```yaml
# crossvpc-eni-alerts.yaml
groups:
- name: crossvpc-eni
  rules:
  # 分配失败率告警
  - alert: CrossVPCEniAllocationFailureRateHigh
    expr: |
      (
        rate(cce_crossvpc_eni_allocation_total{result="failed"}[5m]) /
        rate(cce_crossvpc_eni_allocation_total[5m])
      ) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "CrossVPC ENI allocation failure rate is high"
      description: "CrossVPC ENI allocation failure rate is {{ $value | humanizePercentage }} on node {{ $labels.node }}"
      
  # 分配延迟告警
  - alert: CrossVPCEniAllocationLatencyHigh
    expr: |
      histogram_quantile(0.95, 
        rate(cce_crossvpc_eni_allocation_duration_seconds_bucket[5m])
      ) > 120
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CrossVPC ENI allocation latency is high"
      description: "95th percentile of CrossVPC ENI allocation latency is {{ $value }}s on node {{ $labels.node }}"
      
  # 资源池利用率告警
  - alert: CrossVPCEniPoolUtilizationHigh
    expr: cce_crossvpc_eni_pool_utilization_ratio > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "CrossVPC ENI pool utilization is high"
      description: "CrossVPC ENI pool utilization is {{ $value | humanizePercentage }} on node {{ $labels.node }}, subnet {{ $labels.subnet_id }}"
      
  # 云平台API错误告警
  - alert: CrossVPCCloudAPIErrorRateHigh
    expr: |
      (
        rate(cce_crossvpc_cloud_api_call_total{result="failed"}[5m]) /
        rate(cce_crossvpc_cloud_api_call_total[5m])
      ) > 0.05
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "CrossVPC cloud API error rate is high"
      description: "CrossVPC cloud API error rate is {{ $value | humanizePercentage }} for operation {{ $labels.api_operation }} on node {{ $labels.node }}"
      
  # ENI配额告警
  - alert: CrossVPCEniQuotaExhausted
    expr: increase(cce_crossvpc_eni_error_total{error_code="QuotaExceeded"}[5m]) > 0
    for: 0m
    labels:
      severity: critical
    annotations:
      summary: "CrossVPC ENI quota exhausted"
      description: "CrossVPC ENI quota has been exhausted on node {{ $labels.node }}"
```

## 4. 日志记录

### 4.1 结构化日志

```go
// pkg/endpoint/logging.go
func LogCrossVPCOperation(operation, result string, config *ccev2.CrossVPCAllocationConfig, duration time.Duration, err error) {
    fields := logrus.Fields{
        "component":     "crossvpc-allocator",
        "operation":     operation,
        "result":        result,
        "duration_ms":   duration.Milliseconds(),
        "mode":          config.Mode,
        "userID":        config.UserID,
        "subnetID":      config.SubnetID,
        "vpcCIDR":       config.VPCCIDR,
    }
    
    if err != nil {
        fields["error"] = err.Error()
        if cloudErr, ok := err.(CloudPlatformError); ok {
            fields["error_code"] = cloudErr.Code
            fields["error_retryable"] = cloudErr.IsRetryable()
        }
    }
    
    if result == "success" {
        log.WithFields(fields).Info("CrossVPC operation completed successfully")
    } else {
        log.WithFields(fields).Error("CrossVPC operation failed")
    }
}

func LogCrossVPCCloudAPICall(operation, result string, duration time.Duration, err error) {
    fields := logrus.Fields{
        "component":     "crossvpc-cloud-api",
        "api_operation": operation,
        "result":        result,
        "duration_ms":   duration.Milliseconds(),
    }
    
    if err != nil {
        fields["error"] = err.Error()
    }
    
    if result == "success" {
        log.WithFields(fields).Debug("Cloud API call completed successfully")
    } else {
        log.WithFields(fields).Error("Cloud API call failed")
    }
}
```

## 5. 故障排查指南

### 5.1 常见问题诊断

```bash
# 1. 检查CrossVPC ENI分配状态
kubectl get cep -l crossvpc-eni=true -o wide

# 2. 检查分配失败的CEP
kubectl get cep -o json | jq '.items[] | select(.status.state == "Failed" and .spec.network.ipAllocation.crossVPCConfig != null)'

# 3. 查看CrossVPC相关日志
kubectl logs -n kube-system -l app=cce-network-v2-agent --tail=100 | grep -i crossvpc

# 4. 检查监控指标
curl -s http://localhost:9090/metrics | grep crossvpc_eni

# 5. 检查节点上的ENI状态
kubectl describe node <node-name> | grep -A 20 "CrossVPC"
```

### 5.2 故障排查流程

```mermaid
flowchart TD
    A[CrossVPC ENI分配失败] --> B{检查CEP状态}
    B -->|Pending| C[检查Operator日志]
    B -->|Failed| D[检查错误信息]
    
    C --> E{Operator是否处理}
    E -->|否| F[检查Operator运行状态]
    E -->|是| G[检查云平台API调用]
    
    D --> H{错误类型}
    H -->|配置错误| I[检查Pod注解]
    H -->|云平台错误| J[检查配额和权限]
    H -->|网络错误| K[检查网络连通性]
    
    F --> L[重启Operator]
    G --> M[检查API限流和配额]
    I --> N[修正Pod配置]
    J --> O[调整配额或权限]
    K --> P[修复网络问题]
```

这个详细的错误处理和监控方案提供了完整的CrossVPC ENI运维保障，包括分层错误处理、重试机制、监控指标、告警规则和故障排查指南，确保系统的稳定性和可观测性。
