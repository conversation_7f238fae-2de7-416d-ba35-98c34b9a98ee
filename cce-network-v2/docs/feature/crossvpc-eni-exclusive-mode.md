# CrossVPCEni 独占模式解决方案

## 1. 方案概述

独占模式下，每个Pod独占一个ENI设备，ENI设备直接移动到Pod的网络命名空间中，提供最佳的网络性能和隔离性。

### 1.1 特点

- **网络性能**：直接使用ENI设备，无额外网络开销
- **网络隔离**：每个Pod独占ENI，完全隔离
- **资源消耗**：每个Pod消耗一个ENI资源
- **删除策略**：Pod删除时立即删除ENI

### 1.2 架构图

```mermaid
flowchart TD
    subgraph "Pod Network Namespace"
        Pod[Pod]
        ENI[ENI Device]
    end
    
    subgraph "Host Network"
        CNI[CrossVPC CNI Plugin]
        Agent[CCE Network Agent]
    end
    
    subgraph "Kubernetes Control Plane"
        CEP[CCEEndpoint]
        ENIC[ENI CR]
        Operator[CrossVPC Operator]
        ENISM[ENI State Machine]
    end
    
    subgraph "Cloud Platform"
        Cloud[BCE Cloud API]
    end
    
    Pod --> ENI
    CNI --> Agent
    Agent --> CEP
    Operator --> CEP
    Operator --> ENIC
    ENISM --> ENIC
    ENISM --> Cloud
    
    style ENI fill:#ffcdd2
    style CNI fill:#e3f2fd
    style Agent fill:#fff3e0
    style Operator fill:#e8f5e8
```

## 2. CNI插件实现

### 2.1 CNI配置

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-network",
  "type": "crossvpc-primary",
  "crossvpc": {
    "userID": "user123",
    "subnetID": "sbn-abc123",
    "securityGroups": ["sg-123456"],
    "vpcCIDR": "***********/16",
    "privateIPAddress": "*************",  // 可选，指定IP
    "defaultRouteInterfaceDelegation": true,  // 可选，默认路由委托
    "defaultRouteExcludedCidrs": ["10.0.0.0/8"]  // 可选，排除的CIDR
  },
  "ipam": {
    "type": "cce-network-agent"
  }
}
```

### 2.2 CNI ADD流程

```mermaid
sequenceDiagram
    participant Runtime as Container Runtime
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant Host as Host Network

    Note over Runtime,Host: 【独占模式 CNI ADD流程】
    
    Runtime->>CNI: CNI ADD调用
    Note right of CNI: 参数：<br/>- netns: /proc/123/ns/net<br/>- ifname: eth0<br/>- config: crossvpc配置
    
    CNI->>CNI: 解析CNI配置
    Note right of CNI: 解析：<br/>- type: crossvpc-primary<br/>- userID, subnetID<br/>- securityGroups
    
    CNI->>Agent: 请求IP分配
    Note right of Agent: IPAM请求：<br/>POST /ipam/allocate<br/>body: crossvpc配置
    
    Agent->>CNI: 返回网络配置
    Note right of CNI: 响应：<br/>- ip: *************<br/>- gateway: ***********<br/>- interface: eni-abc123<br/>- mac: 02:00:00:12:34:56
    
    CNI->>Host: 根据MAC地址查找ENI设备
    Note right of Host: 在host netns中查找<br/>MAC地址匹配的网卡
    
    CNI->>CNI: 移动ENI设备到容器netns
    Note right of CNI: ip link set eni-abc123<br/>netns /proc/123/ns/net
    
    CNI->>CNI: 配置IP和路由
    Note right of CNI: - 设置IP地址<br/>- 配置默认路由<br/>- 设置接口状态UP
    
    CNI->>Runtime: 返回成功结果
```

### 2.3 CNI DEL流程

```mermaid
sequenceDiagram
    participant Runtime as Container Runtime
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant Host as Host Network

    Note over Runtime,Host: 【独占模式 CNI DEL流程】
    
    Runtime->>CNI: CNI DEL调用
    CNI->>CNI: 解析容器ID和网络配置
    
    CNI->>CNI: 移动ENI设备回host netns
    Note right of CNI: ip link set eni-abc123<br/>netns 1 (host netns)
    
    CNI->>Agent: 请求IP释放
    Note right of Agent: IPAM请求：<br/>POST /ipam/release<br/>body: 容器标识
    
    Agent->>CNI: 返回释放结果
    CNI->>Runtime: 返回成功结果
    
    Note right of Host: ENI设备返回host，<br/>等待Operator删除
```

## 3. Agent实现

### 3.1 Agent ADD流程

```mermaid
sequenceDiagram
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant CEP as CCEEndpoint
    participant K8s as Kubernetes API

    Note over CNI,K8s: 【独占模式 Agent ADD流程】
    
    CNI->>Agent: POST /ipam/allocate
    Note right of Agent: 请求体：<br/>- containerID<br/>- netns<br/>- crossvpcConfig
    
    Agent->>Agent: 查找对应的Pod
    Note right of Agent: 根据containerID<br/>查找Pod信息
    
    Agent->>CEP: 创建CCEEndpoint
    Note right of CEP: spec:<br/>- type: CrossVPCPrimary<br/>- crossVPCConfig<br/>- nodeName
    
    Agent->>K8s: 提交CEP到K8s
    K8s-->>Agent: CEP创建成功
    
    loop 等待IP分配 (最多5分钟)
        Agent->>CEP: 查询CEP状态
        CEP-->>Agent: 返回当前状态
        
        alt 分配成功
            Agent->>CNI: 返回网络配置
            Note right of CNI: 响应：<br/>- ip, gateway<br/>- interface, mac
        else 分配失败
            Agent->>CNI: 返回错误信息
        else 分配中
            Agent->>Agent: 等待2秒后重试
        end
    end
```

### 3.2 Agent DEL流程

```mermaid
sequenceDiagram
    participant CNI as CrossVPC CNI Plugin
    participant Agent as CCE Network Agent
    participant CEP as CCEEndpoint
    participant K8s as Kubernetes API

    Note over CNI,K8s: 【独占模式 Agent DEL流程】
    
    CNI->>Agent: POST /ipam/release
    Note right of Agent: 请求体：<br/>- containerID<br/>- netns
    
    Agent->>Agent: 查找对应的CEP
    Note right of Agent: 根据containerID<br/>查找CEP资源
    
    alt 找到CEP
        Agent->>K8s: 删除CEP
        Note right of K8s: kubectl delete cep <name>
        K8s-->>Agent: 删除成功
        Agent->>CNI: 返回成功
    else 未找到CEP
        Agent->>CNI: 返回成功 (幂等)
    end
```

## 4. Operator实现

### 4.1 独占模式ADD流程

```mermaid
sequenceDiagram
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over CEP,Cloud: 【独占模式ADD流程】
    
    CEP->>Operator: CEP创建事件
    Note right of CEP: type: CrossVPCPrimary
    
    Operator->>Operator: 可重入检查
    Note right of Operator: 检查是否已有ENI CR
    
    alt 无现有ENI CR
        Operator->>ENIC: 创建ENI CR
        Note right of ENIC: spec:<br/>- useMode: CrossVPCPrimary<br/>- instanceID: node-123<br/>- crossVPCConfig<br/>- deletionPolicy: immediate
        
        ENISM->>ENIC: 监听ENI CR创建
        ENISM->>Cloud: 创建ENI (不指定instanceID)
        Cloud-->>ENISM: 返回ENI ID
        ENISM->>Cloud: 挂载ENI到实例
        Cloud-->>ENISM: 挂载完成
        ENISM->>ENIC: 更新ENI状态 (包含MAC地址)
        
        Operator->>CEP: 更新CEP状态
        Note right of CEP: status:<br/>- ip: *************<br/>- eniID: eni-abc123<br/>- macAddress: 02:00:00:12:34:56<br/>- state: Ready
        
    else 有现有ENI CR
        Operator->>ENIC: 查询ENI CR状态
        Operator->>CEP: 更新CEP状态
    end
```

### 4.2 独占模式删除流程

```mermaid
sequenceDiagram
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant ENIC as ENI CR
    participant ENISM as ENI State Machine
    participant Cloud as Cloud API

    Note over CEP,Cloud: 【独占模式删除流程】
    
    CEP->>Operator: CEP删除事件
    
    Operator->>ENIC: 标记ENI CR删除
    Note right of ENIC: 添加deletionTimestamp
    
    ENISM->>ENIC: 监听删除事件
    ENISM->>Cloud: 分离ENI
    Note right of Cloud: DetachNetworkInterface
    ENISM->>Cloud: 删除ENI
    Note right of Cloud: DeleteNetworkInterface
    ENISM->>ENIC: 移除finalizer
    
    Operator->>CEP: 清理CEP资源
```

## 5. 配置示例

### 5.1 Operator配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  crossvpc-eni-enabled: "true"
  crossvpc-eni-exclusive-mode-enabled: "true"
```

### 5.2 Pod示例

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
  annotations:
    cce.io/network-type: "crossvpc-primary"
spec:
  containers:
  - name: test
    image: nginx
```

独占模式提供了最佳的网络性能和隔离性，适用于对网络性能要求较高的应用场景。
