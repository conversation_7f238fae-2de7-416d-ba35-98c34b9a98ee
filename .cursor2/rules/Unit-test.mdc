---
description: 
globs: *_test.go,*test*
alwaysApply: false
---

# 单元测试生成规则
- ** 对于生成完成对应的测试代码后，请立即执行。首先需要根据 cce-network-v2/Makefile 文件，使用命令 make test-env 来创建对应的测试镜像。接下来使用对应的命令为 docker run --rm -it cce-network-test \
  go test -v -coverprofile=coverage.out -run 函数名 文件位置 来测试对应的单测内容。如果测试执行遇到问题请持续修改出现问题的部分，直到最终可执行。
- ** 你可以分批针对每个需要测试的函数单独创建单测函数，但是同一个测试文件，你需要最终在一个_test文件中实现单测

## 1. 测试用例覆盖范围
- **正常功能路径**：包括典型参数组合。
- **边界条件**：零值、空值、极大/极小值、类型边界。
- **错误处理**：无效输入、错误返回值、超时场景。
- **并发安全**：如有并发逻辑。
- **状态组合**：多调用顺序产生的不同状态。

## 2. Mock接口函数
- 需要mock对应的接口函数。

## 3. 代码覆盖率
- 满足85%以上的行单测覆盖率。
- 如果最后的单测覆盖率不能达到目标，你可以考虑优化正在测试的函数的源码，使得单测可以达标。修改原始函数代码需要向我确认并解释后才能执行修改。【这很重要】
- 对不可达代码添加`//gocover:ignore`注释说明原因。

## 4. 执行逻辑
- 中间过程要尽可能详细展示执行逻辑。

## 5. 测试用例命名
- 函数xxx对应的测试函数用 Testxxx来进行命名
- 每个函数case的名称使用 `Given-When-Then` 格式：
  TestXxx_Given[条件]_When[操作]_Then[结果]


## 6. 测试代码格式参考
- 包组织方式
- 断言库使用习惯
- 子测试组织风格
- 日志验证方式（如需要）
如果后续没有提供可供参考的单测代码，你可以参考这种格式的代码风格

```go
package podcidr

import (
    "context"
    "fmt"
    "net"
    "sync/atomic"
    "testing"
    "time"

    . "gopkg.in/check.v1"
    corev1 "k8s.io/api/core/v1"
    v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/operator/watchers"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/checker"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/controller"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/ipam"
    ipamOption "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/ipam/option"
    ipamTypes "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/ipam/types"
    v2 "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s/apis/cce.baidubce.com/v2"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/option"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/trigger"
    "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/test/mock/ccemock"
    "github.com/stretchr/testify/assert"
)

func Test(t *testing.T) {
    TestingT(t)
}

type PodCIDRSuite struct{}

var _ = Suite(&PodCIDRSuite{})

func mustNewCIDRs(cidrs ...string) []*net.IPNet {
    ipnets := make([]*net.IPNet, 0, len(cidrs))
    for _, cidr := range cidrs {
        _, ipNet, err := net.ParseCIDR(cidr)
        if err != nil {
            panic(err)
        }
        ipnets = append(ipnets, ipNet)
    }
    return ipnets
}

func mustNewTrigger(f func(), minInterval time.Duration) *trigger.Trigger {
    t, err := trigger.NewTrigger(trigger.Parameters{
        MinInterval: minInterval,
        TriggerFunc: func(reasons []string) {
            f()
        },
        Name: "",
    })
    if err != nil {
        panic(err)
    }
    return t
}

var defaultIPAMModes = []string{ipamOption.IPAMClusterPool}

func runWithIPAMModes(ipamModes []string, testFunc func(mode string)) {
    oldIPAMMode := option.Config.IPAM
    defer func() {
        option.Config.IPAM = oldIPAMMode
    }()
    for _, ipamMode := range ipamModes {
        option.Config.IPAM = ipamMode
        testFunc(ipamMode)
    }
}

type mockCIDRAllocator struct {
    OnOccupy       func(cidr *net.IPNet) error
    OnAllocateNext func() (*net.IPNet, error)
    OnRelease      func(cidr *net.IPNet) error
    OnIsAllocated  func(cidr *net.IPNet) (bool, error)
    OnIsIPv6       func() bool
    OnIsFull       func() bool
    OnInRange      func(cidr *net.IPNet) bool
}

func (d *mockCIDRAllocator) String() string {
    return "clusterCIDR: 10.0.0.0/24, nodeMask: 24"
}

func (d *mockCIDRAllocator) Occupy(cidr *net.IPNet) error {
    if d.OnOccupy != nil {
        return d.OnOccupy(cidr)
    }
    panic("d.Occupy should not have been called!")
}

func (d *mockCIDRAllocator) AllocateNext() (*net.IPNet, error) {
    if d.OnAllocateNext != nil {
        return d.OnAllocateNext()
    }
    panic("d.AllocateNext should not have been called!")
}

func (d *mockCIDRAllocator) Release(cidr *net.IPNet) error {
    if d.OnRelease != nil {
        return d.OnRelease(cidr)
    }
    panic("d.Release should not have been called!")
}

func (d *mockCIDRAllocator) IsAllocated(cidr *net.IPNet) (bool, error) {
    if d.OnIsAllocated != nil {
        return d.OnIsAllocated(cidr)
    }
    panic("d.IsAllocated should not have been called!")
}

func (d *mockCIDRAllocator) IsIPv6() bool {
    if d.OnIsIPv6 != nil {
        return d.OnIsIPv6()
    }
    panic("d.IsIPv6 should not have been called!")
}

func (d *mockCIDRAllocator) IsFull() bool {
    if d.OnIsFull != nil {
        return d.OnIsFull()
    }
    panic("d.IsFull should not have been called!")
}

func (d *mockCIDRAllocator) InRange(cidr *net.IPNet) bool {
    if d.OnInRange != nil {
        return d.OnInRange(cidr)
    }
    panic("d.InRange should not have been called!")
}

type k8sNodeMock struct {
    OnUpdate       func(oldNode, newNode *v2.NetResourceSet) (*v2.NetResourceSet, error)
    OnUpdateStatus func(oldNode, newNode *v2.NetResourceSet) (*v2.NetResourceSet, error)
    OnGet          func(node string) (*v2.NetResourceSet, error)
    OnCreate       func(n *v2.NetResourceSet) (*v2.NetResourceSet, error)
    OnDelete       func(nodeName string) error
}

func (k *k8sNodeMock) Update(origNode, node *v2.NetResourceSet) (*v2.NetResourceSet, error) {
    if k.OnUpdate != nil {
        return k.OnUpdate(origNode, node)
    }
    panic("d.Update should not be called!")
}

func (k *k8sNodeMock) UpdateStatus(origNode, node *v2.NetResourceSet) (*v2.NetResourceSet, error) {
    if k.OnUpdateStatus != nil {
        return k.OnUpdateStatus(origNode, node)
    }
    panic("d.UpdateStatus should not be called!")
}

func (k *k8sNodeMock) Get(node string) (*v2.NetResourceSet, error) {
    if k.OnGet != nil {
        return k.OnGet(node)
    }
    panic("d.Get should not be called!")
}

func (k *k8sNodeMock) Create(n *v2.NetResourceSet) (*v2.NetResourceSet, error) {
    if k.OnCreate != nil {
        return k.OnCreate(n)
    }
    panic("d.Create should not be called!")
}

func (s *PodCIDRSuite) TestNodesPodCIDRManager_Create(c *C) {
    var reSyncCalls int32
    type fields struct {
        k8sReSyncController  *controller.Manager
        k8sReSync            *trigger.Trigger
        canAllocateNodes     bool
        v4ClusterCIDRs       []CIDRAllocator
        v6ClusterCIDRs       []CIDRAllocator
        nodes                map[string]*nodeCIDRs
        netResourceSetsToK8s map[string]*netResourceSetK8sOp
    }
    type args struct {
        node *v2.NetResourceSet
    }
    tests := []struct {
        testSetup   func() *fields
        testPostRun func(fields *fields)
        name        string
        fields      *fields
        args        args
        want        error
    }{
        {
            name: "test-1 - should allocate a v4 addr",
            want: nil,
            testSetup: func() *fields {
                atomic.StoreInt32(&reSyncCalls, 0)
                return &fields{
                    canAllocateNodes: true,
                    v4ClusterCIDRs: []CIDRAllocator{
                        &mockCIDRAllocator{
                            OnAllocateNext: func() (ipNet *net.IPNet, err error) {
                                return mustNewCIDRs("*********/24")[0], nil
                            },
                            OnIsFull: func() bool {
                                return false
                            },
                        },
                    },
                    nodes:                map[string]*nodeCIDRs{},
                    netResourceSetsToK8s: map[string]*netResourceSetK8sOp{},
                    k8sReSync: mustNewTrigger(func() {
                        atomic.AddInt32(&reSyncCalls, 1)
                        return
                    }, time.Millisecond),
                }
            },
            testPostRun: func(fields *fields) {
                time.Sleep(2 * time.Millisecond)
                c.Assert(fields.nodes, checker.DeepEquals, map[string]*nodeCIDRs{
                    "node-1": {
                        v4PodCIDRs: mustNewCIDRs("*********/24"),
                    },
                })
                c.Assert(fields.netResourceSetsToK8s, checker.DeepEquals, map[string]*netResourceSetK8sOp{
                    "node-1": {
                        netResourceSet: &v2.NetResourceSet{
                            ObjectMeta: v1.ObjectMeta{
                                Name: "node-1",
                            },
                            Spec: v2.NetResourceSpec{
                                IPAM: ipamTypes.IPAMSpec{
                                    PodCIDRs: []string{
                                        "*********/24",
                                    },
                                },
                            },
                        },
                        op: k8sOpCreate,
                    },
                })
                c.Assert(atomic.LoadInt32(&reSyncCalls), Equals, int32(1))
            },
            args: args{
                node: &v2.NetResourceSet{
                    ObjectMeta: v1.ObjectMeta{
                        Name: "node-1",
                    },
                },
            },
        },
        {
            name: "test-2 - failed to allocate a v4 addr",
            want: nil,
            testSetup: func() *fields {
                atomic.StoreInt32(&reSyncCalls, 0)
                return &fields{
                    canAllocateNodes: true,
                    v4ClusterCIDRs: []CIDRAllocator{
                        &mockCIDRAllocator{
                            OnAllocateNext: func() (ipNet *net.IPNet, err error) {
                                return nil, fmt.Errorf("Allocator full!")
                            },
                            OnIsFull: func() bool {
                                return false
                            },
                        },
                    },
                    nodes:                map[string]*nodeCIDRs{},
                    netResourceSetsToK8s: map[string]*netResourceSetK8sOp{},
                    k8sReSync: mustNewTrigger(func() {
                        atomic.AddInt32(&reSyncCalls, 1)
                        return
                    }, time.Millisecond),
                }
            },
            testPostRun: func(fields *fields) {
                time.Sleep(2 * time.Millisecond)
                c.Assert(fields.nodes, checker.DeepEquals, map[string]*nodeCIDRs{})
                c.Assert(fields.netResourceSetsToK8s, checker.DeepEquals, map[string]*netResourceSetK8sOp{
                    "node-1": {
                        netResourceSet: &v2.NetResourceSet{
                            ObjectMeta: v1.ObjectMeta{
                                Name: "node-1",
                            },
                            Status: v2.NetResourceStatus{
                                IPAM: ipamTypes.IPAMStatus{
                                    OperatorStatus: ipamTypes.OperatorStatus{
                                        Error: "Allocator full!",
                                    },
                                },
                            },
                        },
                        op: k8sOpCreate,
                    },
                })
                c.Assert(atomic.LoadInt32(&reSyncCalls), Equals, int32(1))
            },
            args: args{
                node: &v2.NetResourceSet{
                    ObjectMeta: v1.ObjectMeta{
                        Name: "node-1",
                    },
                },
            },
        },
        {
            name: "test-3 - node is already allocated with the requested pod CIDRs",
            want: nil,
            testSetup: func() *fields {
                return &fields{
                    canAllocateNodes: true,
                    v4ClusterCIDRs: []CIDRAllocator{
                        &mockCIDRAllocator{},
                    },
                    nodes: map[string]*nodeCIDRs{
                        "node-1": {
                            v4PodCIDRs: mustNewCIDRs("*********/24"),
                        },
                    },
                }
            },
            testPostRun: func(fields *fields) {
                c.Assert(fields.nodes, checker.DeepEquals, map[string]*nodeCIDRs{
                    "node-1": {
                        v4PodCIDRs: mustNewCIDRs("*********/24"),
                    },
                })
            },
            args: args{
                node: &v2.NetResourceSet{
                    ObjectMeta: v1.ObjectMeta{
                        Name: "node-1",
                    },
                    Spec: v2.NetResourceSpec{
                        IPAM: ipamTypes.IPAMSpec{
                            PodCIDRs: []string{
                                "*********/24",
                            },
                        },
                    },
                },
            },
        },
        {
            name: "test-4 - node is requesting pod CIDRs, it's already locally allocated but the spec is not updated",
            want: nil,
            testSetup: func() *fields {
                atomic.StoreInt32(&reSyncCalls, 0)
                return &fields{
                    canAllocateNodes: true,
                    nodes: map[string]*nodeCIDRs{
                        "node-1": {
                            v4PodCIDRs: mustNewCIDRs("*********/24"),
                        },
                    },
                    netResourceSetsToK8s: map[string]*netResourceSetK8sOp{},
                    k8sReSync: mustNewTrigger(func() {
                        atomic.AddInt32(&reSyncCalls, 1)
                        return
                    }, time.Millisecond),
                }
            },
            testPostRun: func(fields *fields) {
                time.Sleep(2 * time.Millisecond)
                c.Assert(fields.nodes, checker.DeepEquals, map[string]*nodeCIDRs{
                    "node-1": {
                        v4PodCIDRs: mustNewCIDRs("*********/24"),
                    },
                })
                c.Assert(fields.netResourceSetsToK8s, checker.DeepEquals, map[string]*netResourceSetK8sOp{
                    "node-1": {
                        netResourceSet: &v2.NetResourceSet{
                            ObjectMeta: v1.ObjectMeta{
                                Name: "node-1",
                            },
                            Spec: v2.NetResourceSpec{
                                IPAM: ipamTypes.IPAMSpec{
                                    PodCIDRs: []string{
                                        "*********/24",
                                    },
                                },
                            },
                        },
                        op: k8sOpCreate,
                    },
                })
                c.Assert(atomic.LoadInt32(&reSyncCalls), Equals, int32(1))
            },
            args: args{
                node: &v2.NetResourceSet{
                    ObjectMeta: v1.ObjectMeta{
                        Name: "node-1",
                    },
                },
            },
        },
        {
            name: "test-5 - node requires a new CIDR but the first allocator is full",
            want: nil,
            testSetup: func() *fields {
                atomic.StoreInt32(&reSyncCalls, 0)
                return &fields{
                    canAllocateNodes: true,
                    v4ClusterCIDRs: []CIDRAllocator{
                        &mockCIDRAllocator{
                            OnIsFull: func() bool {
                                return true
                            },
                        },
                        &mockCIDRAllocator{
                            OnAllocateNext: func() (ipNet *net.IPNet, err error) {
                                return mustNewCIDRs("*********/24")[0], nil
                            },
                            OnIsFull: func() bool {
                                return false
                            },
                        },
                    },
                    nodes:                map[string]*nodeCIDRs{},
                    netResourceSetsToK8s: map[string]*netResourceSetK8sOp{},
                    k8sReSync: mustNewTrigger(func() {
                        atomic.AddInt32(&reSyncCalls, 1)
                        return
                    }, time.Millisecond),
                }
            },
            testPostRun: func(fields *fields) {
                time.Sleep(2 * time.Millisecond)
                c.Assert(fields.nodes, checker.DeepEquals, map[string]*nodeCIDRs{
                    "node-1": {
                        v4PodCIDRs: mustNewCIDRs("*********/24"),
                    },
                })
                c.Assert(fields.netResourceSetsToK8s, checker.DeepEquals, map[string]*netResourceSetK8sOp{
                    "node-1": {
                        netResourceSet: &v2.NetResourceSet{
                            ObjectMeta: v1.ObjectMeta{
                                Name: "node-1",
                            },
                            Spec: v2.NetResourceSpec{
                                IPAM: ipamTypes.IPAMSpec{
                                    PodCIDRs: []string{
                                        "*********/24",
                                    },
                                },
                            },
                        },
                        op: k8sOpCreate,
                    },
                })
                c.Assert(atomic.LoadInt32(&reSyncCalls), Equals, int32(1))
            },
            args: args{
                node: &v2.NetResourceSet{
                    ObjectMeta: v1.ObjectMeta{
                        Name: "node-1",
                    },
                },
            },
        },
    }

    runWithIPAMModes(defaultIPAMModes, func(ipamMode string) {
        for _, tt := range tests {
            c.Logf("Running %q (ipam: %s)", tt.name, ipamMode)
            tt.fields = tt.testSetup()
            n := &NodesPodCIDRManager{
                k8sReSyncController:  tt.fields.k8sReSyncController,
                k8sReSync:            tt.fields.k8sReSync,
                canAllocatePodCIDRs:  tt.fields.canAllocateNodes,
                v4CIDRAllocators:     tt.fields.v4ClusterCIDRs,
                v6CIDRAllocators:     tt.fields.v6ClusterCIDRs,
                nodes:                tt.fields.nodes,
                netResourceSetsToK8s: tt.fields.netResourceSetsToK8s,
            }
            got := n.Create(tt.args.node)
            c.Assert(got, checker.Equals, tt.want, Commentf("Test Name: %s", tt.name))

            if tt.testPostRun != nil {
                tt.testPostRun(tt.fields)
            }
        }
    })
}
