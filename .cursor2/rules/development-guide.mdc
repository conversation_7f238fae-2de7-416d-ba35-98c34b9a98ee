---
description: 
globs: 
alwaysApply: false
---
# 开发指南

本指南帮助开发者了解如何在此代码库中进行开发。

## 构建系统

项目使用Makefile管理构建过程。主要的构建目标包括：

- [Makefile](mdc:Makefile) - 根目录Makefile
- [cce-network-v2/Makefile](mdc:cce-network-v2/Makefile) - cce-network-v2组件Makefile
- [eip-operator/Makefile](mdc:eip-operator/Makefile) - eip-operator组件Makefile

## 常用命令

### 构建整个项目

```sh
make build
```

### 构建cce-network-v2

```sh
cd cce-network-v2
make build
```

### 构建eip-operator

```sh
cd eip-operator
make docker-build
```

## 代码规范

- 使用Go 1.21+
- 遵循标准Go代码风格
- 使用golangci-lint进行代码检查
- 所有新功能必须有单元测试

## 版本管理

- 遵循语义化版本控制(SemVer)
- 主版本号：不兼容的API更改
- 次版本号：向后兼容的功能新增
- 修订号：向后兼容的问题修复

## 贡献指南

1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request
