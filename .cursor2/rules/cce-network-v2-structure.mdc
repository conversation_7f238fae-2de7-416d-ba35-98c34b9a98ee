---
description: 
globs: 
alwaysApply: false
---
# cce-network-v2 组件结构

`cce-network-v2` 是CCE容器网络的核心组件，提供了VPC-ENI和VPC-Route网络模式。

## 目录结构

- [cmd](mdc:cce-network-v2/cmd) - 包含各个命令行入口点
  - [agent](mdc:cce-network-v2/cmd/agent) - agent程序入口点
  - [operator](mdc:cce-network-v2/cmd/operator) - operator程序入口点
  - [cnitool](mdc:cce-network-v2/cmd/cnitool) - CNI工具入口点

- [pkg](mdc:cce-network-v2/pkg) - 主要功能包
  - [ipam](mdc:cce-network-v2/pkg/ipam) - IP地址管理
  - [node](mdc:cce-network-v2/pkg/node) - 节点管理
  - [k8s](mdc:cce-network-v2/pkg/k8s) - Kubernetes交互
  - [eni](mdc:cce-network-v2/pkg/eni) - ENI弹性网卡管理
  - [endpoint](mdc:cce-network-v2/pkg/endpoint) - 终端点管理

- [api](mdc:cce-network-v2/api) - API定义
  - 包含CRD资源定义如NetResourceSet, ENI等

- [plugins](mdc:cce-network-v2/plugins) - CNI插件实现
  - 包含多种网络插件的实现

- [docs](mdc:cce-network-v2/docs) - 文档
  - [vpc-eni](mdc:cce-network-v2/docs/vpc-eni) - VPC-ENI网络模式文档
  - [vpc-route](mdc:cce-network-v2/docs/vpc-route) - VPC-Route网络模式文档

## 主要数据结构

1. **NetResourceSet** - 与Kubernetes Node对象对应，描述节点上的网络资源
2. **ENI** - 描述VPC内的弹性网卡
3. **Subnet** - 描述VPC子网资源
4. **CCEEndpoint** - 记录网络端点状态信息
