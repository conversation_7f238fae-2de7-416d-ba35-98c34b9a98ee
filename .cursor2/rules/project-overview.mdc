---
description: 
globs: 
alwaysApply: false
---
# 百度云CNI驱动项目概述

本项目是百度云容器引擎(CCE)的CNI网络插件，实现了CNI与百度云网络基础设施之间的接口。

## 主要组件

项目由以下主要组件组成：

1. **cce-network-v2** - 容器网络的v2版本，针对网络内部状态和可用性进行了优化。提供了适用于BCE云的网络模式，如：
   - [VPC-ENI](mdc:cce-network-v2/docs/vpc-eni/vpc-eni.md) - 容器地址与节点地址使用相同的网段
   - [VPC-Route](mdc:cce-network-v2/docs/vpc-route/vpc-route.md) - 利用VPC提供的自定义路由规则，使容器虚拟网络地址段可访问整个VPC

2. **eip-operator** - 直接为Pod分配EIP，帮助Pod直接连接互联网

## 系统要求

- 推荐的Linux内核版本 >= 5.10
- Golang版本 >= 1.21
- Kubernetes版本 >= 1.20

## 文档和设计

详细的设计文档可在各组件的docs目录中找到：
- [VPC-ENI设计](mdc:cce-network-v2/docs/vpc-eni/vpc-eni.md)
- [VPC-Route设计](mdc:cce-network-v2/docs/vpc-route/vpc-route.md)
