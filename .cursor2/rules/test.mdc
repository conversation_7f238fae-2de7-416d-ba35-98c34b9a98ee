---
description: 
globs: 
alwaysApply: false
---

# 单元测试生成规则
- ** 对于生成完成对应的测试代码后，请立即执行。使用对应的命令为 go test --cover  -gcflags="-N -l" -v -run 函数名 文件位置。如果测试执行遇到问题请持续修改出现问题的部分，直到最终可执行。
- ** 你可以分批针对每个需要测试的函数单独创建单测函数，但是同一个测试文件，你需要最终在一个_test文件中实现单测

## 1. 测试用例覆盖范围
- **正常功能路径**：包括典型参数组合。
- **边界条件**：零值、空值、极大/极小值、类型边界。
- **错误处理**：无效输入、错误返回值、超时场景。
- **并发安全**：如有并发逻辑。
- **状态组合**：多调用顺序产生的不同状态。

## 2. Mock接口函数
- 需要mock对应的接口函数。

## 3. 代码覆盖率
- 满足85%以上的行单测覆盖率。
- 对不可达代码添加`//gocover:ignore`注释说明原因。

## 4. 执行逻辑
- 中间过程要尽可能详细展示执行逻辑。

## 5. 测试用例命名
- 使用 `Given-When-Then` 格式：

  TestXxx_Given[条件]_When[操作]_Then[结果]


## 6. 测试代码格式参考
- 包组织方式
- 断言库使用习惯
- 子测试组织风格
- 日志验证方式（如需要）
如果后续没有提供可供参考的单测代码，你可以参考这种格式的代码风格