---
description: 
globs: 
alwaysApply: false
---
# CNI 插件架构

CNI (Container Network Interface) 插件是容器编排系统与网络实现之间的接口规范。百度云CNI插件实现了此规范，以便在百度云环境中管理容器网络。

## 插件组件

主要插件位于 [cce-network-v2/plugins](mdc:cce-network-v2/plugins) 目录：

- **cptp** - 处理VPC-Route网络模式下的容器网络流量
- **veth** - 管理veth设备对，连接容器和主机网络命名空间
- **vpc-eni** - 管理VPC-ENI网络模式下的弹性网卡及辅助IP

## CNI 规范集成

项目遵循CNI规范，实现了以下接口：

- ADD - 添加容器到网络
- DEL - 从网络中移除容器
- CHECK - 检查容器网络配置
- VERSION - 返回支持的CNI规范版本

## 数据路径

### VPC-ENI 模式

1. Pod创建触发CNI插件
2. vpc-eni插件请求分配ENI辅助IP
3. 为Pod配置网络接口，分配IP地址
4. 配置路由和网络策略

### VPC-Route 模式

1. Pod创建触发CNI插件
2. cptp插件请求分配Pod CIDR中的IP
3. 为Pod配置网络接口，分配IP地址
4. 配置路由表以确保VPC可访问Pod IP

## 插件配置

CNI插件配置通常位于 `/etc/cni/net.d/` 目录，包含插件类型、版本和特定参数：

```json
{
  "cniVersion": "0.3.1",
  "name": "cce-cni",
  "type": "cce-cni",
  "mode": "vpc-route",  // 或 "vpc-eni"
  "log": {
    "level": "debug"
  }
}
```
