---
description: 
globs: 
alwaysApply: false
---
# CCE 网络模式详解

CCE 容器网络提供了多种网络模式，以满足不同的使用场景。

## VPC-ENI 模式

[VPC-ENI](mdc:cce-network-v2/docs/vpc-eni/vpc-eni.md) 是一种打通百度智能云VPC的容器网络模式：

- Pod的IP地址是ENI的辅助IP，地址分配自VPC子网
- 支持固定IP和跨子网分配IP策略
- 兼容BCC/BBC/EBC机型，同时兼容主网卡辅助IP和ENI辅助IP模式
- 支持IPv6
- BCC支持跨子网分配IP

### 重要概念

1. **NetResourceSet** - 每个k8s Node对应一个NetResourceSet对象，描述节点上已经绑定的ENI和ENI辅助IP与Pod之间的分配情况
2. **ENI** - 弹性网卡对象
3. **Subnet** - 子网对象

### 使用限制

- 容器网络的ENI不支持用户手动管理
- 已有ENI的云主机无法加入VPC-ENI容器网络
- 所有节点必须在同一个VPC内
- ENI和云主机实例必须属于同一个可用区

## VPC-Route 模式

[VPC-Route](mdc:cce-network-v2/docs/vpc-route/vpc-route.md) 模式利用VPC路由表将容器IP暴露到整个VPC：

- 容器IP范围和VPC CIDR范围不同，但VPC内所有实例可以访问容器IP
- 单机容器IP数量无限制，可以灵活扩容
- 使用分布式IPAM，节点和容器弹性能力强
- 性能较高，容器使用underlay网络无额外封装性能损耗

### 重要概念

1. **NetResourceSet** - 记录节点上的路由CIDR信息
2. **CCEEndpoint** - 记录网络端点状态信息

### 使用限制

- 每个节点有独立CIDR，不支持固定IP
- 每个VPC的路由表条目默认最多50条
- 推荐内核版本5.7以上
