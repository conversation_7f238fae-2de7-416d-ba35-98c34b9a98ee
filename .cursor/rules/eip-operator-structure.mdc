---
description: 
globs: 
alwaysApply: false
---
# eip-operator 组件结构

`eip-operator` 是一个Kubernetes Operator，用于直接为Pod分配EIP，帮助Pod直接连接互联网。

## 目录结构

- [cmd](mdc:eip-operator/cmd) - 包含命令行入口点
  - [manager](mdc:eip-operator/cmd/manager) - operator管理器入口点

- [internal](mdc:eip-operator/internal) - 内部实现
  - [controller](mdc:eip-operator/internal/controller) - 控制器实现
  - [reconciler](mdc:eip-operator/internal/reconciler) - 调和器实现

- [api](mdc:eip-operator/api) - API定义
  - 包含CRD资源定义

- [config](mdc:eip-operator/config) - 配置文件
  - [crd](mdc:eip-operator/config/crd) - 自定义资源定义
  - [rbac](mdc:eip-operator/config/rbac) - 权限控制配置
  - [manager](mdc:eip-operator/config/manager) - 管理器配置

- [helm](mdc:eip-operator/helm) - Helm chart
  - 用于Kubernetes部署的Helm chart

## 功能

1. 将EIP直接分配给Pod
2. 提供固定IP地址功能
3. 帮助Pod与外部网络直接通信

## 部署

可以通过Helm chart或直接使用kubectl应用manifest来部署eip-operator。
